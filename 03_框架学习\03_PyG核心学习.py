"""
GNN学习第四课：PyTorch Geometric核心学习
====================================

专注于核心的GNN概念和实现，避免复杂的可视化。
学习重点：
1. PyG数据格式
2. 不同GNN层的使用
3. 模型训练和评估
4. 性能比较
"""

import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv
from torch_geometric.data import Data
import numpy as np
from sklearn.datasets import make_classification

def create_simple_graph_data():
    """创建简单的图数据用于学习"""
    print("🔧 创建简单图数据...")
    
    # 创建一个小图：6个节点，每个节点3个特征
    num_nodes = 6
    num_features = 3
    num_classes = 2
    
    # 手工创建节点特征
    x = torch.FloatTensor([
        [1.0, 0.5, 0.2],  # 节点0
        [0.8, 1.2, 0.1],  # 节点1
        [1.5, 0.3, 0.9],  # 节点2
        [0.2, 1.8, 0.4],  # 节点3
        [1.1, 0.7, 0.8],  # 节点4
        [0.6, 1.4, 0.3],  # 节点5
    ])
    
    # 创建边：形成一个连通图
    edge_index = torch.LongTensor([
        [0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 0],  # 源节点
        [1, 0, 2, 1, 3, 2, 4, 3, 5, 4, 0, 5],  # 目标节点
    ])
    
    # 创建标签（基于特征的简单规则）
    y = torch.LongTensor([0, 1, 0, 1, 0, 1])
    
    # 创建掩码
    train_mask = torch.BoolTensor([True, True, True, True, False, False])
    test_mask = torch.BoolTensor([False, False, False, False, True, True])
    
    # 创建PyG数据对象
    data = Data(x=x, edge_index=edge_index, y=y, 
                train_mask=train_mask, test_mask=test_mask)
    
    print(f"✅ 图数据创建完成:")
    print(f"  节点数: {data.num_nodes}")
    print(f"  边数: {data.num_edges}")
    print(f"  特征维度: {data.num_node_features}")
    print(f"  类别数: {num_classes}")
    print(f"  训练节点: {train_mask.sum()}")
    print(f"  测试节点: {test_mask.sum()}")
    
    return data, num_classes


def create_larger_graph_data():
    """创建较大的合成图数据"""
    print("🔧 创建较大的合成图数据...")
    
    num_nodes = 100
    num_features = 5
    num_classes = 3
    
    # 使用sklearn生成分类数据
    X, y = make_classification(
        n_samples=num_nodes,
        n_features=num_features,
        n_classes=num_classes,
        n_informative=3,
        n_redundant=0,
        random_state=42
    )
    
    x = torch.FloatTensor(X)
    y = torch.LongTensor(y)
    
    # 创建随机图结构
    np.random.seed(42)
    edge_list = []
    
    # 为每个节点随机连接几个其他节点
    for i in range(num_nodes):
        num_connections = np.random.randint(2, 8)  # 每个节点连接2-7个其他节点
        targets = np.random.choice(num_nodes, num_connections, replace=False)
        for j in targets:
            if i != j:
                edge_list.append([i, j])
    
    edge_index = torch.LongTensor(edge_list).t().contiguous()
    
    # 创建训练/测试掩码
    num_train = int(0.7 * num_nodes)
    train_mask = torch.zeros(num_nodes, dtype=torch.bool)
    test_mask = torch.zeros(num_nodes, dtype=torch.bool)
    
    train_mask[:num_train] = True
    test_mask[num_train:] = True
    
    data = Data(x=x, edge_index=edge_index, y=y,
                train_mask=train_mask, test_mask=test_mask)
    
    print(f"✅ 图数据创建完成:")
    print(f"  节点数: {data.num_nodes}")
    print(f"  边数: {data.num_edges}")
    print(f"  特征维度: {data.num_node_features}")
    print(f"  训练节点: {train_mask.sum()}")
    print(f"  测试节点: {test_mask.sum()}")
    
    return data, num_classes


class SimpleGCN(torch.nn.Module):
    """简单的GCN模型"""
    
    def __init__(self, input_dim, hidden_dim, output_dim):
        super(SimpleGCN, self).__init__()
        self.conv1 = GCNConv(input_dim, hidden_dim)
        self.conv2 = GCNConv(hidden_dim, output_dim)
        print(f"🧠 创建GCN: {input_dim} -> {hidden_dim} -> {output_dim}")
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


class SimpleGAT(torch.nn.Module):
    """简单的GAT模型"""
    
    def __init__(self, input_dim, hidden_dim, output_dim, heads=4):
        super(SimpleGAT, self).__init__()
        self.conv1 = GATConv(input_dim, hidden_dim, heads=heads)
        self.conv2 = GATConv(hidden_dim * heads, output_dim, heads=1)
        print(f"🧠 创建GAT: {input_dim} -> {hidden_dim}*{heads} -> {output_dim}")
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


class SimpleSAGE(torch.nn.Module):
    """简单的GraphSAGE模型"""
    
    def __init__(self, input_dim, hidden_dim, output_dim):
        super(SimpleSAGE, self).__init__()
        self.conv1 = SAGEConv(input_dim, hidden_dim)
        self.conv2 = SAGEConv(hidden_dim, output_dim)
        print(f"🧠 创建GraphSAGE: {input_dim} -> {hidden_dim} -> {output_dim}")
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


def train_and_test_model(model, data, epochs=100, lr=0.01):
    """训练和测试模型"""
    print(f"🚀 开始训练模型 (epochs={epochs})...")
    
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    criterion = torch.nn.NLLLoss()
    
    model.train()
    
    for epoch in range(epochs):
        optimizer.zero_grad()
        out = model(data.x, data.edge_index)
        loss = criterion(out[data.train_mask], data.y[data.train_mask])
        loss.backward()
        optimizer.step()
        
        if epoch % 20 == 0:
            train_acc = accuracy(out[data.train_mask], data.y[data.train_mask])
            print(f'  Epoch {epoch:03d}: Loss={loss:.4f}, Train Acc={train_acc:.4f}')
    
    # 测试
    model.eval()
    with torch.no_grad():
        out = model(data.x, data.edge_index)
        test_acc = accuracy(out[data.test_mask], data.y[data.test_mask])
    
    print(f"✅ 训练完成，测试准确率: {test_acc:.4f}")
    return test_acc


def accuracy(pred, target):
    """计算准确率"""
    pred_class = pred.argmax(dim=1)
    return (pred_class == target).float().mean().item()


def compare_gnn_models(data, num_classes):
    """比较不同的GNN模型"""
    print("\n🏆 比较不同GNN模型")
    print("=" * 50)
    
    input_dim = data.num_node_features
    hidden_dim = 8
    
    # 创建不同的模型
    models = {
        'GCN': SimpleGCN(input_dim, hidden_dim, num_classes),
        'GAT': SimpleGAT(input_dim, hidden_dim, num_classes, heads=2),
        'GraphSAGE': SimpleSAGE(input_dim, hidden_dim, num_classes)
    }
    
    results = {}
    
    for name, model in models.items():
        print(f"\n--- 训练 {name} ---")
        torch.manual_seed(42)  # 确保公平比较
        test_acc = train_and_test_model(model, data, epochs=100)
        results[name] = test_acc
    
    return results


def demonstrate_message_passing(data):
    """演示消息传递过程"""
    print("\n🔄 演示消息传递过程")
    print("=" * 40)
    
    print("原始节点特征:")
    for i, features in enumerate(data.x):
        print(f"  节点{i}: {features.numpy()}")
    
    print("\n边连接关系:")
    edge_index = data.edge_index
    for i in range(edge_index.shape[1]):
        src, dst = edge_index[0, i].item(), edge_index[1, i].item()
        print(f"  {src} -> {dst}")
    
    # 手动演示一轮消息传递
    print("\n手动演示消息传递:")
    
    # 为每个节点聚合邻居特征
    num_nodes = data.num_nodes
    aggregated_features = torch.zeros_like(data.x)
    
    for node in range(num_nodes):
        # 找到这个节点的邻居
        neighbors = []
        for i in range(edge_index.shape[1]):
            if edge_index[1, i].item() == node:  # 如果这条边指向当前节点
                neighbors.append(edge_index[0, i].item())
        
        if neighbors:
            # 聚合邻居特征（简单平均）
            neighbor_features = data.x[neighbors]
            aggregated = torch.mean(neighbor_features, dim=0)
            aggregated_features[node] = aggregated
            
            print(f"  节点{node}的邻居: {neighbors}")
            print(f"  聚合后特征: {aggregated.numpy()}")
        else:
            print(f"  节点{node}没有邻居")


def understand_pyg_data_format(data):
    """理解PyG数据格式"""
    print("\n📦 理解PyG数据格式")
    print("=" * 40)
    
    print("PyG Data对象包含的属性:")
    print(f"  x (节点特征): {data.x.shape} - {data.x.dtype}")
    print(f"  edge_index (边索引): {data.edge_index.shape} - {data.edge_index.dtype}")
    print(f"  y (节点标签): {data.y.shape} - {data.y.dtype}")
    print(f"  train_mask: {data.train_mask.shape} - {data.train_mask.dtype}")
    print(f"  test_mask: {data.test_mask.shape} - {data.test_mask.dtype}")
    
    print("\n边索引格式说明:")
    print("edge_index是一个2×E的矩阵，其中E是边的数量")
    print("第一行是源节点，第二行是目标节点")
    print("例如：")
    for i in range(min(5, data.edge_index.shape[1])):
        src, dst = data.edge_index[0, i].item(), data.edge_index[1, i].item()
        print(f"  边{i}: 节点{src} -> 节点{dst}")
    
    print("\n掩码的作用:")
    print("train_mask: 标记哪些节点用于训练")
    print("test_mask: 标记哪些节点用于测试")
    print(f"训练节点索引: {torch.where(data.train_mask)[0].tolist()}")
    print(f"测试节点索引: {torch.where(data.test_mask)[0].tolist()}")


if __name__ == "__main__":
    print("🚀 欢迎来到GNN核心学习！")
    print("专注于理解PyTorch Geometric的核心概念\n")
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    print("=" * 60)
    print("第一部分：小图示例")
    print("=" * 60)
    
    # 创建小图数据
    small_data, small_num_classes = create_simple_graph_data()
    
    # 理解数据格式
    understand_pyg_data_format(small_data)
    
    # 演示消息传递
    demonstrate_message_passing(small_data)
    
    # 在小图上比较模型
    small_results = compare_gnn_models(small_data, small_num_classes)
    
    print("\n=" * 60)
    print("第二部分：较大图示例")
    print("=" * 60)
    
    # 创建较大的图数据
    large_data, large_num_classes = create_larger_graph_data()
    
    # 在较大图上比较模型
    large_results = compare_gnn_models(large_data, large_num_classes)
    
    print("\n🎯 实验结果总结")
    print("=" * 40)
    
    print("小图结果:")
    for model, acc in small_results.items():
        print(f"  {model:>10}: {acc:.4f}")
    
    print("\n大图结果:")
    for model, acc in large_results.items():
        print(f"  {model:>10}: {acc:.4f}")
    
    print("\n💡 关键学习点:")
    print("1. PyG使用edge_index格式表示图结构")
    print("2. 不同GNN层实现不同的消息传递策略")
    print("3. GCN使用简单的邻居平均")
    print("4. GAT使用注意力机制加权邻居")
    print("5. GraphSAGE使用采样和聚合")
    
    print("\n🎉 恭喜！你已经掌握了PyTorch Geometric的核心用法！")
