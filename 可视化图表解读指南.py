"""
GNN可视化图表解读指南
===================

这个文件帮助你理解复杂的可视化图表，特别是混淆矩阵等概念。
我们将用简单的例子和清晰的解释来说明每个部分的含义。
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from sklearn.metrics import confusion_matrix

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def explain_confusion_matrix():
    """详细解释混淆矩阵"""
    print("🔍 混淆矩阵详解")
    print("=" * 50)
    
    print("混淆矩阵是什么？")
    print("• 它是一个方形表格，显示模型预测的准确性")
    print("• 行代表真实标签，列代表预测标签")
    print("• 数字表示每种情况的样本数量")
    
    # 创建一个简单的例子
    print("\n📝 简单例子：")
    print("假设我们要预测用户影响力：低、中、高")
    
    # 模拟数据
    true_labels = [0, 0, 0, 1, 1, 1, 2, 2, 2, 2]  # 真实标签
    pred_labels = [0, 0, 1, 1, 1, 2, 2, 2, 2, 1]  # 预测标签
    
    cm = confusion_matrix(true_labels, pred_labels)
    
    print(f"真实标签: {true_labels}")
    print(f"预测标签: {pred_labels}")
    print(f"\n混淆矩阵:")
    print(cm)
    
    print(f"\n📊 如何读懂这个矩阵：")
    print(f"• 左上角 {cm[0,0]}：真实是'低'，预测也是'低' → 预测正确！")
    print(f"• 第1行第2列 {cm[0,1]}：真实是'低'，预测成'中' → 预测错误")
    print(f"• 对角线上的数字：预测正确的数量")
    print(f"• 非对角线的数字：预测错误的数量")
    
    return cm

def create_simple_visualization():
    """创建简单易懂的可视化"""
    print("\n🎨 创建简单的可视化示例")
    print("=" * 50)
    
    # 创建一个简单的混淆矩阵
    cm = np.array([[8, 1, 0],
                   [2, 6, 1], 
                   [0, 1, 7]])
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 1. 基本混淆矩阵
    ax1 = axes[0]
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax1,
                xticklabels=['低影响力', '中影响力', '高影响力'],
                yticklabels=['低影响力', '中影响力', '高影响力'])
    ax1.set_title('混淆矩阵示例', fontsize=14)
    ax1.set_xlabel('预测标签')
    ax1.set_ylabel('真实标签')
    
    # 添加解释文字
    ax1.text(1.5, -0.5, '数字越大 = 该类预测越准确', 
             ha='center', transform=ax1.transData, fontsize=10,
             bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))
    
    # 2. 模型性能对比
    ax2 = axes[1]
    models = ['GCN', 'GAT', 'GraphSAGE']
    accuracies = [0.65, 0.72, 0.89]
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    bars = ax2.bar(models, accuracies, color=colors)
    for bar, acc in zip(bars, accuracies):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{acc:.2f}', ha='center', va='bottom', fontweight='bold')
    
    ax2.set_title('模型准确率对比', fontsize=14)
    ax2.set_ylabel('准确率')
    ax2.set_ylim(0, 1)
    ax2.grid(True, alpha=0.3)
    
    # 添加解释
    ax2.text(1, 0.3, '柱子越高 = 模型越好', 
             ha='center', fontsize=10,
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7))
    
    # 3. 训练过程
    ax3 = axes[2]
    epochs = range(50)
    loss_gcn = [1.0 - 0.8 * (1 - np.exp(-x/20)) + 0.1 * np.random.random() for x in epochs]
    loss_gat = [1.0 - 0.85 * (1 - np.exp(-x/25)) + 0.1 * np.random.random() for x in epochs]
    loss_sage = [1.0 - 0.9 * (1 - np.exp(-x/15)) + 0.1 * np.random.random() for x in epochs]
    
    ax3.plot(epochs, loss_gcn, label='GCN', color='#FF6B6B', alpha=0.8)
    ax3.plot(epochs, loss_gat, label='GAT', color='#4ECDC4', alpha=0.8)
    ax3.plot(epochs, loss_sage, label='GraphSAGE', color='#45B7D1', alpha=0.8)
    
    ax3.set_title('训练损失变化', fontsize=14)
    ax3.set_xlabel('训练轮数 (Epoch)')
    ax3.set_ylabel('损失值 (Loss)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 添加解释
    ax3.text(25, 0.8, '线条向下 = 模型在学习', 
             ha='center', fontsize=10,
             bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('可视化图表解读示例.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 简化版可视化已保存为 '可视化图表解读示例.png'")

def explain_each_chart_component():
    """解释原始图表中的每个组件"""
    print("\n📋 原始复杂图表的各部分解释")
    print("=" * 50)
    
    components = {
        "🕸️ 网络图（左上角）": {
            "是什么": "显示用户之间的连接关系",
            "颜色含义": "红色=低影响力, 青色=中影响力, 蓝色=高影响力",
            "线条含义": "用户之间的关注/好友关系"
        },
        
        "📊 柱状图（中上）": {
            "是什么": "对比三个模型的准确率",
            "高度含义": "柱子越高，模型预测越准确",
            "数字含义": "具体的准确率数值"
        },
        
        "📈 折线图（右上）": {
            "是什么": "显示训练过程中损失的变化",
            "趋势含义": "线条下降表示模型在学习进步",
            "颜色含义": "不同颜色代表不同的模型"
        },
        
        "🔲 小方框（下排）": {
            "是什么": "混淆矩阵 - 最重要的评估工具",
            "数字含义": "每个格子里的数字表示预测情况的数量",
            "颜色含义": "颜色越深，数字越大"
        },
        
        "📊 条形图（左下）": {
            "是什么": "特征重要性分析",
            "长度含义": "条形越长，该特征对预测影响力越重要",
            "用途": "帮助理解哪些用户特征最关键"
        },
        
        "🥧 饼图（中下）": {
            "是什么": "用户影响力分布",
            "扇形含义": "每个扇形代表一类用户的比例",
            "颜色含义": "与网络图的颜色对应"
        }
    }
    
    for component, details in components.items():
        print(f"\n{component}:")
        for key, value in details.items():
            print(f"  {key}: {value}")

def practical_tips():
    """实用的图表阅读技巧"""
    print("\n💡 实用的图表阅读技巧")
    print("=" * 50)
    
    tips = [
        "🎯 看重点：先看标题，了解图表要表达什么",
        "🔢 看数字：关注具体的数值，比文字更准确",
        "🌈 看颜色：相同颜色通常代表相同类别",
        "📏 看大小：柱子高度、点的大小都有含义",
        "📊 看趋势：折线图的上升下降表示变化方向",
        "🔍 看细节：图例、坐标轴标签包含重要信息",
        "🤔 问自己：这个图想告诉我什么结论？"
    ]
    
    for tip in tips:
        print(f"  {tip}")
    
    print(f"\n🎓 对于混淆矩阵，记住这个口诀：")
    print(f"  '对角线上数字大，模型预测就不差！'")
    print(f"  '非对角数字小，分类效果顶呱呱！'")

if __name__ == "__main__":
    print("🎓 GNN可视化图表解读指南")
    print("=" * 60)
    print("让我们一步步理解复杂的可视化图表！\n")
    
    # 1. 解释混淆矩阵
    cm = explain_confusion_matrix()
    
    # 2. 创建简单示例
    create_simple_visualization()
    
    # 3. 解释各个组件
    explain_each_chart_component()
    
    # 4. 实用技巧
    practical_tips()
    
    print(f"\n🎉 现在你应该能看懂那些'小方框'了！")
    print(f"它们是混淆矩阵，是评估AI模型好坏的重要工具。")
    print(f"记住：对角线数字大 = 模型好！")
