"""
GNN学习成果展示：社交网络影响力分析
================================

这个案例展示如何使用图神经网络分析社交网络中的用户影响力。
我们将：
1. 生成一个真实的社交网络数据
2. 使用多种GNN模型预测用户影响力
3. 可视化分析结果和网络结构
4. 对比不同模型的性能

这是一个完整的端到端GNN应用案例！
"""

import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv
from torch_geometric.data import Data
import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
import seaborn as sns
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

class SocialNetworkGenerator:
    """社交网络数据生成器"""
    
    def __init__(self, num_users=200, seed=42):
        self.num_users = num_users
        self.seed = seed
        np.random.seed(seed)
        torch.manual_seed(seed)
    
    def generate_network(self):
        """生成社交网络数据"""
        print("🌐 生成社交网络数据...")
        
        # 生成用户特征
        user_features = self._generate_user_features()
        
        # 生成网络结构
        edge_index = self._generate_network_structure()
        
        # 计算影响力标签
        influence_labels = self._calculate_influence_labels(user_features, edge_index)
        
        # 创建训练/测试掩码
        train_mask, test_mask = self._create_masks()
        
        # 创建PyG数据对象
        data = Data(
            x=user_features,
            edge_index=edge_index,
            y=influence_labels,
            train_mask=train_mask,
            test_mask=test_mask
        )
        
        print(f"✅ 网络生成完成:")
        print(f"  用户数量: {data.num_nodes}")
        print(f"  连接数量: {data.num_edges}")
        print(f"  特征维度: {data.num_node_features}")
        print(f"  训练用户: {train_mask.sum()}")
        print(f"  测试用户: {test_mask.sum()}")
        
        return data
    
    def _generate_user_features(self):
        """生成用户特征"""
        # 特征包括：粉丝数、发帖数、活跃度、账户年龄、认证状态
        features = []
        
        for i in range(self.num_users):
            # 粉丝数 (对数正态分布)
            followers = np.random.lognormal(mean=5, sigma=2)
            
            # 发帖数 (泊松分布)
            posts = np.random.poisson(lam=50)
            
            # 活跃度 (0-1之间)
            activity = np.random.beta(a=2, b=2)
            
            # 账户年龄 (1-10年)
            account_age = np.random.uniform(1, 10)
            
            # 认证状态 (二元特征)
            verified = np.random.choice([0, 1], p=[0.9, 0.1])
            
            features.append([followers, posts, activity, account_age, verified])
        
        # 标准化特征
        features = np.array(features)
        features = (features - features.mean(axis=0)) / (features.std(axis=0) + 1e-8)
        
        return torch.FloatTensor(features)
    
    def _generate_network_structure(self):
        """生成网络结构"""
        # 使用小世界网络模型
        G = nx.watts_strogatz_graph(self.num_users, k=8, p=0.3, seed=self.seed)
        
        # 添加一些随机的长距离连接（模拟网红效应）
        for _ in range(self.num_users // 10):
            u = np.random.randint(0, self.num_users)
            v = np.random.randint(0, self.num_users)
            if u != v:
                G.add_edge(u, v)
        
        # 转换为PyG格式
        edge_list = list(G.edges())
        edge_index = torch.LongTensor(edge_list).t().contiguous()
        
        return edge_index
    
    def _calculate_influence_labels(self, features, edge_index):
        """计算影响力标签"""
        # 基于特征和网络位置计算影响力分数
        
        # 计算度中心性
        degrees = torch.zeros(self.num_users)
        for i in range(edge_index.shape[1]):
            degrees[edge_index[0, i]] += 1
        
        # 综合特征计算影响力分数
        influence_score = (
            features[:, 0] * 0.3 +  # 粉丝数
            features[:, 1] * 0.2 +  # 发帖数
            features[:, 2] * 0.2 +  # 活跃度
            features[:, 4] * 0.1 +  # 认证状态
            (degrees / degrees.max()) * 0.2  # 网络中心性
        )
        
        # 转换为分类标签 (低、中、高影响力)
        labels = torch.zeros(self.num_users, dtype=torch.long)
        thresholds = torch.quantile(influence_score, torch.tensor([0.33, 0.67]))
        
        labels[influence_score >= thresholds[1]] = 2  # 高影响力
        labels[(influence_score >= thresholds[0]) & (influence_score < thresholds[1])] = 1  # 中影响力
        labels[influence_score < thresholds[0]] = 0  # 低影响力
        
        return labels
    
    def _create_masks(self):
        """创建训练/测试掩码"""
        indices = torch.randperm(self.num_users)
        train_size = int(0.7 * self.num_users)
        
        train_mask = torch.zeros(self.num_users, dtype=torch.bool)
        test_mask = torch.zeros(self.num_users, dtype=torch.bool)
        
        train_mask[indices[:train_size]] = True
        test_mask[indices[train_size:]] = True
        
        return train_mask, test_mask


class InfluenceGNN(torch.nn.Module):
    """影响力预测GNN模型"""
    
    def __init__(self, input_dim, hidden_dim, output_dim, model_type='GCN', dropout=0.5):
        super(InfluenceGNN, self).__init__()
        
        self.model_type = model_type
        self.dropout = dropout
        
        if model_type == 'GCN':
            self.conv1 = GCNConv(input_dim, hidden_dim)
            self.conv2 = GCNConv(hidden_dim, output_dim)
        elif model_type == 'GAT':
            self.conv1 = GATConv(input_dim, hidden_dim, heads=4, dropout=dropout)
            self.conv2 = GATConv(hidden_dim * 4, output_dim, heads=1, dropout=dropout)
        elif model_type == 'SAGE':
            self.conv1 = SAGEConv(input_dim, hidden_dim)
            self.conv2 = SAGEConv(hidden_dim, output_dim)
        
        print(f"🧠 创建 {model_type} 模型: {input_dim} -> {hidden_dim} -> {output_dim}")
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=self.dropout, training=self.training)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


class ModelTrainer:
    """模型训练器"""
    
    def __init__(self, data):
        self.data = data
        self.results = {}
    
    def train_model(self, model, epochs=200, lr=0.01):
        """训练单个模型"""
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=5e-4)
        criterion = torch.nn.NLLLoss()
        
        train_losses = []
        train_accs = []
        
        model.train()
        for epoch in range(epochs):
            optimizer.zero_grad()
            out = model(self.data.x, self.data.edge_index)
            loss = criterion(out[self.data.train_mask], self.data.y[self.data.train_mask])
            loss.backward()
            optimizer.step()
            
            # 记录训练指标
            train_acc = self._calculate_accuracy(out[self.data.train_mask], 
                                               self.data.y[self.data.train_mask])
            train_losses.append(loss.item())
            train_accs.append(train_acc)
            
            if epoch % 50 == 0:
                print(f'  Epoch {epoch:03d}: Loss={loss:.4f}, Train Acc={train_acc:.4f}')
        
        return train_losses, train_accs
    
    def evaluate_model(self, model):
        """评估模型"""
        model.eval()
        with torch.no_grad():
            out = model(self.data.x, self.data.edge_index)
            
            # 测试集预测
            test_pred = out[self.data.test_mask].argmax(dim=1)
            test_true = self.data.y[self.data.test_mask]
            
            # 计算指标
            test_acc = accuracy_score(test_true.cpu(), test_pred.cpu())
            
            return {
                'predictions': out,
                'test_accuracy': test_acc,
                'test_pred': test_pred,
                'test_true': test_true
            }
    
    def _calculate_accuracy(self, pred, target):
        """计算准确率"""
        pred_class = pred.argmax(dim=1)
        return (pred_class == target).float().mean().item()
    
    def compare_models(self):
        """比较不同模型"""
        print("\n🏆 训练和比较不同GNN模型")
        print("=" * 60)
        
        models = {
            'GCN': InfluenceGNN(self.data.num_node_features, 32, 3, 'GCN'),
            'GAT': InfluenceGNN(self.data.num_node_features, 16, 3, 'GAT'),
            'GraphSAGE': InfluenceGNN(self.data.num_node_features, 32, 3, 'SAGE')
        }
        
        for name, model in models.items():
            print(f"\n--- 训练 {name} ---")
            
            # 训练模型
            train_losses, train_accs = self.train_model(model, epochs=200)
            
            # 评估模型
            eval_results = self.evaluate_model(model)
            
            # 保存结果
            self.results[name] = {
                'model': model,
                'train_losses': train_losses,
                'train_accs': train_accs,
                'test_accuracy': eval_results['test_accuracy'],
                'predictions': eval_results['predictions'],
                'test_pred': eval_results['test_pred'],
                'test_true': eval_results['test_true']
            }
            
            print(f"✅ {name} 测试准确率: {eval_results['test_accuracy']:.4f}")
        
        return self.results


class ResultVisualizer:
    """结果可视化器"""

    def __init__(self, data, results):
        self.data = data
        self.results = results

        # 创建NetworkX图用于可视化
        edge_list = self.data.edge_index.t().numpy()
        self.G = nx.Graph()
        self.G.add_edges_from(edge_list)

    def create_comprehensive_visualization(self):
        """创建综合可视化展示"""
        print("\n📊 创建可视化展示...")

        # 创建大图布局
        fig = plt.figure(figsize=(20, 16))

        # 1. 网络结构可视化
        self._plot_network_structure(fig)

        # 2. 模型性能对比
        self._plot_model_comparison(fig)

        # 3. 训练过程可视化
        self._plot_training_process(fig)

        # 4. 混淆矩阵
        self._plot_confusion_matrices(fig)

        # 5. 特征重要性分析
        self._plot_feature_analysis(fig)

        # 6. 影响力分布分析
        self._plot_influence_distribution(fig)

        plt.tight_layout()
        plt.savefig('GNN学习成果展示_社交网络影响力分析.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ 可视化图表已保存为 'GNN学习成果展示_社交网络影响力分析.png'")

    def _plot_network_structure(self, fig):
        """绘制网络结构"""
        ax1 = plt.subplot(3, 3, 1)

        # 计算布局
        pos = nx.spring_layout(self.G, k=1, iterations=50)

        # 根据真实影响力着色
        node_colors = []
        color_map = {0: '#FF6B6B', 1: '#4ECDC4', 2: '#45B7D1'}
        for i in range(self.data.num_nodes):
            node_colors.append(color_map[self.data.y[i].item()])

        # 绘制网络
        nx.draw(self.G, pos, node_color=node_colors, node_size=30,
                edge_color='gray', alpha=0.6, ax=ax1)

        ax1.set_title('社交网络结构\n(红色:低影响力, 青色:中影响力, 蓝色:高影响力)', fontsize=12)
        ax1.axis('off')

    def _plot_model_comparison(self, fig):
        """绘制模型性能对比"""
        ax2 = plt.subplot(3, 3, 2)

        models = list(self.results.keys())
        accuracies = [self.results[model]['test_accuracy'] for model in models]

        bars = ax2.bar(models, accuracies, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])

        # 添加数值标签
        for bar, acc in zip(bars, accuracies):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')

        ax2.set_title('模型性能对比', fontsize=12)
        ax2.set_ylabel('测试准确率')
        ax2.set_ylim(0, 1)
        ax2.grid(True, alpha=0.3)

    def _plot_training_process(self, fig):
        """绘制训练过程"""
        ax3 = plt.subplot(3, 3, 3)

        for model_name, result in self.results.items():
            ax3.plot(result['train_losses'], label=f'{model_name} Loss', alpha=0.7)

        ax3.set_title('训练损失变化', fontsize=12)
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Loss')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 训练准确率
        ax4 = plt.subplot(3, 3, 6)
        for model_name, result in self.results.items():
            ax4.plot(result['train_accs'], label=f'{model_name}', alpha=0.7)

        ax4.set_title('训练准确率变化', fontsize=12)
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('Accuracy')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

    def _plot_confusion_matrices(self, fig):
        """绘制混淆矩阵"""
        for i, (model_name, result) in enumerate(self.results.items()):
            ax = plt.subplot(3, 3, 4 + i)

            cm = confusion_matrix(result['test_true'].cpu(), result['test_pred'].cpu())

            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax,
                       xticklabels=['低', '中', '高'],
                       yticklabels=['低', '中', '高'])

            ax.set_title(f'{model_name} 混淆矩阵', fontsize=10)
            ax.set_xlabel('预测标签')
            ax.set_ylabel('真实标签')

    def _plot_feature_analysis(self, fig):
        """绘制特征分析"""
        ax7 = plt.subplot(3, 3, 7)

        # 计算特征与影响力的相关性
        features = self.data.x.numpy()
        labels = self.data.y.numpy()

        feature_names = ['粉丝数', '发帖数', '活跃度', '账户年龄', '认证状态']
        correlations = []

        for i in range(features.shape[1]):
            corr = np.corrcoef(features[:, i], labels)[0, 1]
            correlations.append(abs(corr))

        bars = ax7.barh(feature_names, correlations, color='lightcoral')
        ax7.set_title('特征重要性分析', fontsize=12)
        ax7.set_xlabel('与影响力的相关性')

        # 添加数值标签
        for bar, corr in zip(bars, correlations):
            width = bar.get_width()
            ax7.text(width + 0.01, bar.get_y() + bar.get_height()/2.,
                    f'{corr:.3f}', ha='left', va='center')

    def _plot_influence_distribution(self, fig):
        """绘制影响力分布"""
        ax8 = plt.subplot(3, 3, 8)

        # 影响力分布
        influence_counts = torch.bincount(self.data.y)
        labels = ['低影响力', '中影响力', '高影响力']
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

        wedges, texts, autotexts = ax8.pie(influence_counts, labels=labels, colors=colors,
                                          autopct='%1.1f%%', startangle=90)

        ax8.set_title('用户影响力分布', fontsize=12)

        # 网络统计信息
        ax9 = plt.subplot(3, 3, 9)
        ax9.axis('off')

        # 计算网络统计
        avg_degree = 2 * self.G.number_of_edges() / self.G.number_of_nodes()
        clustering = nx.average_clustering(self.G)

        stats_text = f"""
网络统计信息:
• 节点数量: {self.G.number_of_nodes()}
• 边数量: {self.G.number_of_edges()}
• 平均度数: {avg_degree:.2f}
• 聚类系数: {clustering:.3f}

最佳模型: {max(self.results.keys(), key=lambda x: self.results[x]['test_accuracy'])}
最高准确率: {max(self.results[x]['test_accuracy'] for x in self.results):.3f}

数据生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M')}
        """

        ax9.text(0.1, 0.9, stats_text, transform=ax9.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))


def main():
    """主函数 - 完整的案例演示"""
    print("🎓 GNN学习成果展示：社交网络影响力分析")
    print("=" * 60)
    print("这个案例将展示如何使用图神经网络分析社交网络中的用户影响力")
    print("包括数据生成、模型训练、结果可视化等完整流程\n")

    # 1. 生成社交网络数据
    generator = SocialNetworkGenerator(num_users=200, seed=42)
    data = generator.generate_network()

    # 2. 训练和比较模型
    trainer = ModelTrainer(data)
    results = trainer.compare_models()

    # 3. 创建可视化展示
    visualizer = ResultVisualizer(data, results)
    visualizer.create_comprehensive_visualization()

    # 4. 输出详细结果
    print(f"\n📈 详细结果分析:")
    print("=" * 40)

    for model_name, result in results.items():
        print(f"\n{model_name} 模型:")
        print(f"  测试准确率: {result['test_accuracy']:.4f}")

        # 分类报告
        report = classification_report(
            result['test_true'].cpu(),
            result['test_pred'].cpu(),
            target_names=['低影响力', '中影响力', '高影响力'],
            output_dict=True
        )

        print(f"  精确率: {report['macro avg']['precision']:.4f}")
        print(f"  召回率: {report['macro avg']['recall']:.4f}")
        print(f"  F1分数: {report['macro avg']['f1-score']:.4f}")

    best_model = max(results.keys(), key=lambda x: results[x]['test_accuracy'])
    print(f"\n🏆 最佳模型: {best_model}")
    print(f"🎯 最高准确率: {results[best_model]['test_accuracy']:.4f}")

    print(f"\n🎉 案例展示完成！")
    print(f"可视化结果已保存为图片文件，展示了完整的GNN应用流程。")


if __name__ == "__main__":
    main()
