"""
GNN学习成果展示：社交网络影响力分析
================================

这个案例展示如何使用图神经网络分析社交网络中的用户影响力。
我们将：
1. 生成一个真实的社交网络数据
2. 使用多种GNN模型预测用户影响力
3. 可视化分析结果和网络结构
4. 对比不同模型的性能

这是一个完整的端到端GNN应用案例！
"""

import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv
from torch_geometric.data import Data
import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
import seaborn as sns
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

class SocialNetworkGenerator:
    """社交网络数据生成器"""
    
    def __init__(self, num_users=200, seed=42):
        self.num_users = num_users
        self.seed = seed
        np.random.seed(seed)
        torch.manual_seed(seed)
    
    def generate_network(self):
        """生成社交网络数据"""
        print("🌐 生成社交网络数据...")
        
        # 生成用户特征
        user_features = self._generate_user_features()
        
        # 生成网络结构
        edge_index = self._generate_network_structure()
        
        # 计算影响力标签
        influence_labels = self._calculate_influence_labels(user_features, edge_index)
        
        # 创建训练/测试掩码
        train_mask, test_mask = self._create_masks()
        
        # 创建PyG数据对象
        data = Data(
            x=user_features,
            edge_index=edge_index,
            y=influence_labels,
            train_mask=train_mask,
            test_mask=test_mask
        )
        
        print(f"✅ 网络生成完成:")
        print(f"  用户数量: {data.num_nodes}")
        print(f"  连接数量: {data.num_edges}")
        print(f"  特征维度: {data.num_node_features}")
        print(f"  训练用户: {train_mask.sum()}")
        print(f"  测试用户: {test_mask.sum()}")
        
        return data
    
    def _generate_user_features(self):
        """生成用户特征"""
        # 特征包括：粉丝数、发帖数、活跃度、账户年龄、认证状态
        features = []
        
        for i in range(self.num_users):
            # 粉丝数 (对数正态分布)
            followers = np.random.lognormal(mean=5, sigma=2)
            
            # 发帖数 (泊松分布)
            posts = np.random.poisson(lam=50)
            
            # 活跃度 (0-1之间)
            activity = np.random.beta(a=2, b=2)
            
            # 账户年龄 (1-10年)
            account_age = np.random.uniform(1, 10)
            
            # 认证状态 (二元特征)
            verified = np.random.choice([0, 1], p=[0.9, 0.1])
            
            features.append([followers, posts, activity, account_age, verified])
        
        # 标准化特征
        features = np.array(features)
        features = (features - features.mean(axis=0)) / (features.std(axis=0) + 1e-8)
        
        return torch.FloatTensor(features)
    
    def _generate_network_structure(self):
        """生成网络结构"""
        # 使用小世界网络模型
        G = nx.watts_strogatz_graph(self.num_users, k=8, p=0.3, seed=self.seed)
        
        # 添加一些随机的长距离连接（模拟网红效应）
        for _ in range(self.num_users // 10):
            u = np.random.randint(0, self.num_users)
            v = np.random.randint(0, self.num_users)
            if u != v:
                G.add_edge(u, v)
        
        # 转换为PyG格式
        edge_list = list(G.edges())
        edge_index = torch.LongTensor(edge_list).t().contiguous()
        
        return edge_index
    
    def _calculate_influence_labels(self, features, edge_index):
        """计算影响力标签"""
        # 基于特征和网络位置计算影响力分数
        
        # 计算度中心性
        degrees = torch.zeros(self.num_users)
        for i in range(edge_index.shape[1]):
            degrees[edge_index[0, i]] += 1
        
        # 综合特征计算影响力分数
        influence_score = (
            features[:, 0] * 0.3 +  # 粉丝数
            features[:, 1] * 0.2 +  # 发帖数
            features[:, 2] * 0.2 +  # 活跃度
            features[:, 4] * 0.1 +  # 认证状态
            (degrees / degrees.max()) * 0.2  # 网络中心性
        )
        
        # 转换为分类标签 (低、中、高影响力)
        labels = torch.zeros(self.num_users, dtype=torch.long)
        thresholds = torch.quantile(influence_score, torch.tensor([0.33, 0.67]))
        
        labels[influence_score >= thresholds[1]] = 2  # 高影响力
        labels[(influence_score >= thresholds[0]) & (influence_score < thresholds[1])] = 1  # 中影响力
        labels[influence_score < thresholds[0]] = 0  # 低影响力
        
        return labels
    
    def _create_masks(self):
        """创建训练/测试掩码"""
        indices = torch.randperm(self.num_users)
        train_size = int(0.7 * self.num_users)
        
        train_mask = torch.zeros(self.num_users, dtype=torch.bool)
        test_mask = torch.zeros(self.num_users, dtype=torch.bool)
        
        train_mask[indices[:train_size]] = True
        test_mask[indices[train_size:]] = True
        
        return train_mask, test_mask


class InfluenceGNN(torch.nn.Module):
    """影响力预测GNN模型"""
    
    def __init__(self, input_dim, hidden_dim, output_dim, model_type='GCN', dropout=0.5):
        super(InfluenceGNN, self).__init__()
        
        self.model_type = model_type
        self.dropout = dropout
        
        if model_type == 'GCN':
            self.conv1 = GCNConv(input_dim, hidden_dim)
            self.conv2 = GCNConv(hidden_dim, output_dim)
        elif model_type == 'GAT':
            self.conv1 = GATConv(input_dim, hidden_dim, heads=4, dropout=dropout)
            self.conv2 = GATConv(hidden_dim * 4, output_dim, heads=1, dropout=dropout)
        elif model_type == 'SAGE':
            self.conv1 = SAGEConv(input_dim, hidden_dim)
            self.conv2 = SAGEConv(hidden_dim, output_dim)
        
        print(f"🧠 创建 {model_type} 模型: {input_dim} -> {hidden_dim} -> {output_dim}")
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=self.dropout, training=self.training)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


class ModelTrainer:
    """模型训练器"""
    
    def __init__(self, data):
        self.data = data
        self.results = {}
    
    def train_model(self, model, epochs=200, lr=0.01):
        """训练单个模型"""
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=5e-4)
        criterion = torch.nn.NLLLoss()
        
        train_losses = []
        train_accs = []
        
        model.train()
        for epoch in range(epochs):
            optimizer.zero_grad()
            out = model(self.data.x, self.data.edge_index)
            loss = criterion(out[self.data.train_mask], self.data.y[self.data.train_mask])
            loss.backward()
            optimizer.step()
            
            # 记录训练指标
            train_acc = self._calculate_accuracy(out[self.data.train_mask], 
                                               self.data.y[self.data.train_mask])
            train_losses.append(loss.item())
            train_accs.append(train_acc)
            
            if epoch % 50 == 0:
                print(f'  Epoch {epoch:03d}: Loss={loss:.4f}, Train Acc={train_acc:.4f}')
        
        return train_losses, train_accs
    
    def evaluate_model(self, model):
        """评估模型"""
        model.eval()
        with torch.no_grad():
            out = model(self.data.x, self.data.edge_index)
            
            # 测试集预测
            test_pred = out[self.data.test_mask].argmax(dim=1)
            test_true = self.data.y[self.data.test_mask]
            
            # 计算指标
            test_acc = accuracy_score(test_true.cpu(), test_pred.cpu())
            
            return {
                'predictions': out,
                'test_accuracy': test_acc,
                'test_pred': test_pred,
                'test_true': test_true
            }
    
    def _calculate_accuracy(self, pred, target):
        """计算准确率"""
        pred_class = pred.argmax(dim=1)
        return (pred_class == target).float().mean().item()
    
    def compare_models(self):
        """比较不同模型"""
        print("\n🏆 训练和比较不同GNN模型")
        print("=" * 60)
        
        models = {
            'GCN': InfluenceGNN(self.data.num_node_features, 32, 3, 'GCN'),
            'GAT': InfluenceGNN(self.data.num_node_features, 16, 3, 'GAT'),
            'GraphSAGE': InfluenceGNN(self.data.num_node_features, 32, 3, 'SAGE')
        }
        
        for name, model in models.items():
            print(f"\n--- 训练 {name} ---")
            
            # 训练模型
            train_losses, train_accs = self.train_model(model, epochs=200)
            
            # 评估模型
            eval_results = self.evaluate_model(model)
            
            # 保存结果
            self.results[name] = {
                'model': model,
                'train_losses': train_losses,
                'train_accs': train_accs,
                'test_accuracy': eval_results['test_accuracy'],
                'predictions': eval_results['predictions'],
                'test_pred': eval_results['test_pred'],
                'test_true': eval_results['test_true']
            }
            
            print(f"✅ {name} 测试准确率: {eval_results['test_accuracy']:.4f}")
        
        return self.results
