"""
GNN学习成果展示：社交网络影响力分析
================================

这个案例展示如何使用图神经网络分析社交网络中的用户影响力。
我们将：
1. 生成一个真实的社交网络数据
2. 使用多种GNN模型预测用户影响力
3. 可视化分析结果和网络结构
4. 对比不同模型的性能

这是一个完整的端到端GNN应用案例！
"""

import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv
from torch_geometric.data import Data
import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
import seaborn as sns
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体 - 和简单版本保持一致
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SocialNetworkGenerator:
    """社交网络数据生成器"""
    
    def __init__(self, num_users=200, seed=42):
        self.num_users = num_users
        self.seed = seed
        np.random.seed(seed)
        torch.manual_seed(seed)
    
    def generate_network(self):
        """生成社交网络数据"""
        print("🌐 生成社交网络数据...")
        
        # 生成用户特征
        user_features = self._generate_user_features()
        
        # 生成网络结构
        edge_index = self._generate_network_structure()
        
        # 计算影响力标签
        influence_labels = self._calculate_influence_labels(user_features, edge_index)
        
        # 创建训练/测试掩码
        train_mask, test_mask = self._create_masks()
        
        # 创建PyG数据对象
        data = Data(
            x=user_features,
            edge_index=edge_index,
            y=influence_labels,
            train_mask=train_mask,
            test_mask=test_mask
        )
        
        print(f"✅ 网络生成完成:")
        print(f"  用户数量: {data.num_nodes}")
        print(f"  连接数量: {data.num_edges}")
        print(f"  特征维度: {data.num_node_features}")
        print(f"  训练用户: {train_mask.sum()}")
        print(f"  测试用户: {test_mask.sum()}")
        
        return data
    
    def _generate_user_features(self):
        """生成用户特征"""
        # 特征包括：粉丝数、发帖数、活跃度、账户年龄、认证状态
        features = []
        
        for i in range(self.num_users):
            # 粉丝数 (对数正态分布)
            followers = np.random.lognormal(mean=5, sigma=2)
            
            # 发帖数 (泊松分布)
            posts = np.random.poisson(lam=50)
            
            # 活跃度 (0-1之间)
            activity = np.random.beta(a=2, b=2)
            
            # 账户年龄 (1-10年)
            account_age = np.random.uniform(1, 10)
            
            # 认证状态 (二元特征)
            verified = np.random.choice([0, 1], p=[0.9, 0.1])
            
            features.append([followers, posts, activity, account_age, verified])
        
        # 标准化特征
        features = np.array(features)
        features = (features - features.mean(axis=0)) / (features.std(axis=0) + 1e-8)
        
        return torch.FloatTensor(features)
    
    def _generate_network_structure(self):
        """生成网络结构"""
        # 使用小世界网络模型
        G = nx.watts_strogatz_graph(self.num_users, k=8, p=0.3, seed=self.seed)
        
        # 添加一些随机的长距离连接（模拟网红效应）
        for _ in range(self.num_users // 10):
            u = np.random.randint(0, self.num_users)
            v = np.random.randint(0, self.num_users)
            if u != v:
                G.add_edge(u, v)
        
        # 转换为PyG格式
        edge_list = list(G.edges())
        edge_index = torch.LongTensor(edge_list).t().contiguous()
        
        return edge_index
    
    def _calculate_influence_labels(self, features, edge_index):
        """计算影响力标签"""
        # 基于特征和网络位置计算影响力分数
        
        # 计算度中心性
        degrees = torch.zeros(self.num_users)
        for i in range(edge_index.shape[1]):
            degrees[edge_index[0, i]] += 1
        
        # 综合特征计算影响力分数
        influence_score = (
            features[:, 0] * 0.3 +  # 粉丝数
            features[:, 1] * 0.2 +  # 发帖数
            features[:, 2] * 0.2 +  # 活跃度
            features[:, 4] * 0.1 +  # 认证状态
            (degrees / degrees.max()) * 0.2  # 网络中心性
        )
        
        # 转换为分类标签 (低、中、高影响力)
        labels = torch.zeros(self.num_users, dtype=torch.long)
        thresholds = torch.quantile(influence_score, torch.tensor([0.33, 0.67]))
        
        labels[influence_score >= thresholds[1]] = 2  # 高影响力
        labels[(influence_score >= thresholds[0]) & (influence_score < thresholds[1])] = 1  # 中影响力
        labels[influence_score < thresholds[0]] = 0  # 低影响力
        
        return labels
    
    def _create_masks(self):
        """创建训练/测试掩码"""
        indices = torch.randperm(self.num_users)
        train_size = int(0.7 * self.num_users)
        
        train_mask = torch.zeros(self.num_users, dtype=torch.bool)
        test_mask = torch.zeros(self.num_users, dtype=torch.bool)
        
        train_mask[indices[:train_size]] = True
        test_mask[indices[train_size:]] = True
        
        return train_mask, test_mask


class InfluenceGNN(torch.nn.Module):
    """影响力预测GNN模型"""
    
    def __init__(self, input_dim, hidden_dim, output_dim, model_type='GCN', dropout=0.5):
        super(InfluenceGNN, self).__init__()
        
        self.model_type = model_type
        self.dropout = dropout
        
        if model_type == 'GCN':
            self.conv1 = GCNConv(input_dim, hidden_dim)
            self.conv2 = GCNConv(hidden_dim, output_dim)
        elif model_type == 'GAT':
            self.conv1 = GATConv(input_dim, hidden_dim, heads=4, dropout=dropout)
            self.conv2 = GATConv(hidden_dim * 4, output_dim, heads=1, dropout=dropout)
        elif model_type == 'SAGE':
            self.conv1 = SAGEConv(input_dim, hidden_dim)
            self.conv2 = SAGEConv(hidden_dim, output_dim)
        
        print(f"🧠 创建 {model_type} 模型: {input_dim} -> {hidden_dim} -> {output_dim}")
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=self.dropout, training=self.training)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


class ModelTrainer:
    """模型训练器"""
    
    def __init__(self, data):
        self.data = data
        self.results = {}
    
    def train_model(self, model, epochs=200, lr=0.01):
        """训练单个模型"""
        optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=5e-4)
        criterion = torch.nn.NLLLoss()
        
        train_losses = []
        train_accs = []
        
        model.train()
        for epoch in range(epochs):
            optimizer.zero_grad()
            out = model(self.data.x, self.data.edge_index)
            loss = criterion(out[self.data.train_mask], self.data.y[self.data.train_mask])
            loss.backward()
            optimizer.step()
            
            # 记录训练指标
            train_acc = self._calculate_accuracy(out[self.data.train_mask], 
                                               self.data.y[self.data.train_mask])
            train_losses.append(loss.item())
            train_accs.append(train_acc)
            
            if epoch % 50 == 0:
                print(f'  Epoch {epoch:03d}: Loss={loss:.4f}, Train Acc={train_acc:.4f}')
        
        return train_losses, train_accs
    
    def evaluate_model(self, model):
        """评估模型"""
        model.eval()
        with torch.no_grad():
            out = model(self.data.x, self.data.edge_index)
            
            # 测试集预测
            test_pred = out[self.data.test_mask].argmax(dim=1)
            test_true = self.data.y[self.data.test_mask]
            
            # 计算指标
            test_acc = accuracy_score(test_true.cpu(), test_pred.cpu())
            
            return {
                'predictions': out,
                'test_accuracy': test_acc,
                'test_pred': test_pred,
                'test_true': test_true
            }
    
    def _calculate_accuracy(self, pred, target):
        """计算准确率"""
        pred_class = pred.argmax(dim=1)
        return (pred_class == target).float().mean().item()
    
    def compare_models(self):
        """比较不同模型"""
        print("\n🏆 训练和比较不同GNN模型")
        print("=" * 60)
        
        models = {
            'GCN': InfluenceGNN(self.data.num_node_features, 32, 3, 'GCN'),
            'GAT': InfluenceGNN(self.data.num_node_features, 16, 3, 'GAT'),
            'GraphSAGE': InfluenceGNN(self.data.num_node_features, 32, 3, 'SAGE')
        }
        
        for name, model in models.items():
            print(f"\n--- 训练 {name} ---")
            
            # 训练模型
            train_losses, train_accs = self.train_model(model, epochs=200)
            
            # 评估模型
            eval_results = self.evaluate_model(model)
            
            # 保存结果
            self.results[name] = {
                'model': model,
                'train_losses': train_losses,
                'train_accs': train_accs,
                'test_accuracy': eval_results['test_accuracy'],
                'predictions': eval_results['predictions'],
                'test_pred': eval_results['test_pred'],
                'test_true': eval_results['test_true']
            }
            
            print(f"✅ {name} 测试准确率: {eval_results['test_accuracy']:.4f}")
        
        return self.results


class ResultVisualizer:
    """结果可视化器"""

    def __init__(self, data, results):
        self.data = data
        self.results = results

        # 创建NetworkX图用于可视化
        edge_list = self.data.edge_index.t().numpy()
        self.G = nx.Graph()
        self.G.add_edges_from(edge_list)

    def create_comprehensive_visualization(self):
        """创建真正简洁的可视化展示"""
        print("\n📊 创建真正简洁的可视化展示...")

        # 只创建最重要的3个图：1行3列
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        fig.suptitle('GNN学习成果展示：社交网络影响力分析', fontsize=16, fontweight='bold', y=0.98)

        # 1. 模型性能对比（左）
        self._plot_simple_model_comparison(axes[0])

        # 2. 训练过程（中）
        self._plot_simple_training_process(axes[1])

        # 3. 最佳模型结果（右）
        self._plot_simple_results(axes[2])

        plt.tight_layout()
        plt.subplots_adjust(top=0.85)  # 为标题留空间
        plt.savefig('GNN学习成果展示_超简洁版.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ 超简洁版可视化图表已保存为 'GNN学习成果展示_超简洁版.png'")

    def _plot_simple_model_comparison(self, ax):
        """绘制简洁的模型性能对比"""
        models = list(self.results.keys())
        accuracies = [self.results[model]['test_accuracy'] for model in models]

        # 使用更好看的颜色
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
        bars = ax.bar(models, accuracies, color=colors, alpha=0.8,
                     edgecolor='white', linewidth=2)

        # 添加数值标签
        for bar, acc in zip(bars, accuracies):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                    f'{acc:.0%}', ha='center', va='bottom',
                    fontweight='bold', fontsize=14)

        ax.set_title('模型准确率对比', fontsize=14, fontweight='bold')
        ax.set_ylabel('测试准确率', fontsize=12)
        ax.set_ylim(0, 1.1)
        ax.grid(True, alpha=0.3, axis='y')

        # 美化坐标轴
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)

    def _plot_simple_training_process(self, ax):
        """绘制简洁的训练过程"""
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

        for i, (model_name, result) in enumerate(self.results.items()):
            ax.plot(result['train_losses'], label=f'{model_name}',
                   color=colors[i], linewidth=3, alpha=0.8)

        ax.set_title('训练损失变化', fontsize=14, fontweight='bold')
        ax.set_xlabel('训练轮数', fontsize=12)
        ax.set_ylabel('损失值', fontsize=12)
        ax.legend(fontsize=11)
        ax.grid(True, alpha=0.3)

        # 美化坐标轴
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)

    def _plot_simple_results(self, ax):
        """绘制简洁的结果总结"""
        ax.axis('off')

        # 计算关键统计信息
        best_model = max(self.results.keys(), key=lambda x: self.results[x]['test_accuracy'])
        best_accuracy = self.results[best_model]['test_accuracy']

        # 所有模型的准确率
        all_accs = [self.results[model]['test_accuracy'] for model in self.results.keys()]
        avg_acc = np.mean(all_accs)

        # 创建结果文本
        result_text = f"""
🎯 实验结果总结

📊 数据规模:
• {self.data.num_nodes} 个用户
• {self.data.num_edges} 条连接

🏆 最佳模型:
• {best_model}
• 准确率: {best_accuracy:.0%}

📈 模型对比:
• GCN: {self.results['GCN']['test_accuracy']:.0%}
• GAT: {self.results['GAT']['test_accuracy']:.0%}
• GraphSAGE: {self.results['GraphSAGE']['test_accuracy']:.0%}

✨ 学习成果:
✓ 掌握GNN核心原理
✓ 实现多种GNN架构
✓ 完成端到端项目
✓ 达到{best_accuracy:.0%}预测准确率
        """

        ax.text(0.05, 0.95, result_text, transform=ax.transAxes, fontsize=12,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))

    def _plot_clean_training_process(self, ax):
        """绘制整洁的训练过程"""
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

        for i, (model_name, result) in enumerate(self.results.items()):
            ax.plot(result['train_losses'], label=f'{model_name}',
                   color=colors[i], linewidth=2, alpha=0.8)

        ax.set_title('训练损失变化', fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('训练轮数', fontsize=12)
        ax.set_ylabel('损失值', fontsize=12)
        ax.legend(fontsize=10, frameon=True, fancybox=True, shadow=True)
        ax.grid(True, alpha=0.3)

        # 美化坐标轴
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.tick_params(axis='both', which='major', labelsize=10)

    def _plot_best_model_confusion_matrix(self, ax):
        """绘制最佳模型的混淆矩阵"""
        # 找到最佳模型
        best_model = max(self.results.keys(), key=lambda x: self.results[x]['test_accuracy'])
        result = self.results[best_model]

        cm = confusion_matrix(result['test_true'].cpu(), result['test_pred'].cpu())

        # 使用更美观的热力图
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax,
                   xticklabels=['低', '中', '高'],
                   yticklabels=['低', '中', '高'],
                   cbar_kws={'shrink': 0.8})

        ax.set_title(f'{best_model} 混淆矩阵', fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('预测标签', fontsize=12)
        ax.set_ylabel('真实标签', fontsize=12)

    def _plot_clean_influence_distribution(self, ax):
        """绘制整洁的影响力分布"""
        influence_counts = torch.bincount(self.data.y)
        labels = ['低影响力', '中影响力', '高影响力']
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

        # 创建饼图
        wedges, texts, autotexts = ax.pie(influence_counts, labels=labels, colors=colors,
                                         autopct='%1.1f%%', startangle=90,
                                         textprops={'fontsize': 11})

        # 美化文本
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')

        ax.set_title('用户影响力分布', fontsize=14, fontweight='bold', pad=20)

    def _plot_project_summary(self, ax):
        """绘制项目总结"""
        ax.axis('off')

        # 计算一些统计信息
        best_model = max(self.results.keys(), key=lambda x: self.results[x]['test_accuracy'])
        best_accuracy = self.results[best_model]['test_accuracy']

        # 网络统计
        avg_degree = 2 * self.G.number_of_edges() / self.G.number_of_nodes()

        summary_text = f"""
📊 项目总结

🎯 任务: 社交网络影响力预测
📈 数据规模: {self.data.num_nodes}个用户, {self.data.num_edges}条连接
🧠 模型对比: GCN, GAT, GraphSAGE

🏆 最佳模型: {best_model}
🎯 最高准确率: {best_accuracy:.1%}

📊 网络特征:
• 平均度数: {avg_degree:.1f}
• 影响力分布均匀

✨ 学习成果:
✓ 掌握GNN核心原理
✓ 实现多种GNN模型
✓ 完成端到端项目
✓ 具备实际应用能力
        """

        ax.text(0.05, 0.95, summary_text, transform=ax.transAxes, fontsize=11,
                verticalalignment='top',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))




def main():
    """主函数 - 完整的案例演示"""
    print("🎓 GNN学习成果展示：社交网络影响力分析")
    print("=" * 60)
    print("这个案例将展示如何使用图神经网络分析社交网络中的用户影响力")
    print("包括数据生成、模型训练、结果可视化等完整流程\n")

    # 1. 生成社交网络数据
    generator = SocialNetworkGenerator(num_users=200, seed=42)
    data = generator.generate_network()

    # 2. 训练和比较模型
    trainer = ModelTrainer(data)
    results = trainer.compare_models()

    # 3. 创建可视化展示
    visualizer = ResultVisualizer(data, results)
    visualizer.create_comprehensive_visualization()

    # 4. 输出详细结果
    print(f"\n📈 详细结果分析:")
    print("=" * 40)

    for model_name, result in results.items():
        print(f"\n{model_name} 模型:")
        print(f"  测试准确率: {result['test_accuracy']:.4f}")

        # 分类报告
        report = classification_report(
            result['test_true'].cpu(),
            result['test_pred'].cpu(),
            target_names=['低影响力', '中影响力', '高影响力'],
            output_dict=True
        )

        print(f"  精确率: {report['macro avg']['precision']:.4f}")
        print(f"  召回率: {report['macro avg']['recall']:.4f}")
        print(f"  F1分数: {report['macro avg']['f1-score']:.4f}")

    best_model = max(results.keys(), key=lambda x: results[x]['test_accuracy'])
    print(f"\n🏆 最佳模型: {best_model}")
    print(f"🎯 最高准确率: {results[best_model]['test_accuracy']:.4f}")

    print(f"\n🎉 案例展示完成！")
    print(f"可视化结果已保存为图片文件，展示了完整的GNN应用流程。")


if __name__ == "__main__":
    main()
