"""
GNN学习第三课：手工实现简单GNN
============================

在这个文件中，我们将：
1. 从零开始实现一个简单的GNN
2. 理解消息传递的每一个步骤
3. 看到GNN如何更新节点特征
4. 为使用框架做准备

这是理解GNN工作原理的关键！
"""

import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SimpleGNN:
    """
    手工实现的简单图神经网络
    """
    
    def __init__(self, input_dim, hidden_dim):
        """
        初始化GNN
        
        参数:
        - input_dim: 输入特征维度
        - hidden_dim: 隐藏层维度
        """
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # 初始化权重矩阵（随机初始化）
        # W_self: 处理节点自身特征的权重
        self.W_self = np.random.randn(input_dim, hidden_dim) * 0.1
        
        # W_neighbor: 处理邻居特征的权重
        self.W_neighbor = np.random.randn(input_dim, hidden_dim) * 0.1
        
        print(f"🧠 初始化GNN: 输入维度={input_dim}, 隐藏维度={hidden_dim}")
    
    def aggregate_neighbors(self, node_features, adjacency_matrix, method='mean'):
        """
        聚合邻居特征
        
        参数:
        - node_features: 节点特征矩阵 (n_nodes, feature_dim)
        - adjacency_matrix: 邻接矩阵 (n_nodes, n_nodes)
        - method: 聚合方法 ('mean', 'sum', 'max')
        
        返回:
        - aggregated_features: 聚合后的特征矩阵
        """
        n_nodes = node_features.shape[0]
        aggregated_features = np.zeros_like(node_features)
        
        print(f"\n🔄 开始聚合邻居特征 (方法: {method})")
        
        for i in range(n_nodes):
            # 找到节点i的邻居
            neighbors = np.where(adjacency_matrix[i] == 1)[0]
            
            if len(neighbors) > 0:
                neighbor_features = node_features[neighbors]
                
                # 根据方法聚合
                if method == 'mean':
                    aggregated = np.mean(neighbor_features, axis=0)
                elif method == 'sum':
                    aggregated = np.sum(neighbor_features, axis=0)
                elif method == 'max':
                    aggregated = np.max(neighbor_features, axis=0)
                else:
                    raise ValueError(f"未知的聚合方法: {method}")
                
                aggregated_features[i] = aggregated
                print(f"  节点{i}: {len(neighbors)}个邻居 -> 聚合特征: {aggregated}")
            else:
                # 没有邻居的节点，聚合特征为0
                aggregated_features[i] = np.zeros(node_features.shape[1])
                print(f"  节点{i}: 无邻居 -> 聚合特征: 全零")
        
        return aggregated_features
    
    def forward(self, node_features, adjacency_matrix):
        """
        前向传播
        
        参数:
        - node_features: 节点特征矩阵
        - adjacency_matrix: 邻接矩阵
        
        返回:
        - new_features: 更新后的节点特征
        """
        print("\n🚀 开始GNN前向传播")
        print("=" * 50)
        
        # 步骤1: 聚合邻居特征
        neighbor_features = self.aggregate_neighbors(node_features, adjacency_matrix)
        
        # 步骤2: 变换自身特征
        self_transformed = np.dot(node_features, self.W_self)
        print(f"\n🔧 自身特征变换完成，形状: {self_transformed.shape}")
        
        # 步骤3: 变换邻居特征
        neighbor_transformed = np.dot(neighbor_features, self.W_neighbor)
        print(f"🔧 邻居特征变换完成，形状: {neighbor_transformed.shape}")
        
        # 步骤4: 组合特征
        combined_features = self_transformed + neighbor_transformed
        print(f"🔧 特征组合完成，形状: {combined_features.shape}")
        
        # 步骤5: 激活函数 (ReLU)
        new_features = np.maximum(0, combined_features)
        print(f"🔧 激活函数应用完成")
        
        return new_features
    
    def print_weights(self):
        """打印权重矩阵"""
        print("\n📊 权重矩阵:")
        print("W_self (自身特征权重):")
        print(self.W_self)
        print("\nW_neighbor (邻居特征权重):")
        print(self.W_neighbor)


def create_simple_example():
    """
    创建一个简单的例子来演示GNN
    """
    print("🌟 创建简单示例")
    print("=" * 40)
    
    # 创建一个4节点的图
    # 节点特征：每个节点有2维特征
    node_features = np.array([
        [1.0, 0.5],  # 节点0
        [0.8, 1.2],  # 节点1
        [1.5, 0.3],  # 节点2
        [0.2, 1.8],  # 节点3
    ])
    
    # 邻接矩阵：定义图的结构
    # 0-1, 1-2, 2-3, 3-0 形成一个环
    adjacency_matrix = np.array([
        [0, 1, 0, 1],  # 节点0连接节点1和3
        [1, 0, 1, 0],  # 节点1连接节点0和2
        [0, 1, 0, 1],  # 节点2连接节点1和3
        [1, 0, 1, 0],  # 节点3连接节点0和2
    ])
    
    print("初始节点特征:")
    print(node_features)
    print("\n邻接矩阵:")
    print(adjacency_matrix)
    
    # 创建GNN
    gnn = SimpleGNN(input_dim=2, hidden_dim=3)
    gnn.print_weights()
    
    # 前向传播
    new_features = gnn.forward(node_features, adjacency_matrix)
    
    print("\n✨ 更新后的节点特征:")
    print(new_features)
    
    return node_features, new_features, adjacency_matrix


def compare_aggregation_methods():
    """
    比较不同的聚合方法
    """
    print("\n🔍 比较不同聚合方法")
    print("=" * 40)
    
    # 简单的3节点图
    node_features = np.array([
        [1.0, 2.0],  # 节点A
        [3.0, 1.0],  # 节点B
        [2.0, 3.0],  # 节点C
    ])
    
    # A连接B和C
    adjacency_matrix = np.array([
        [0, 1, 1],  # A连接B和C
        [1, 0, 0],  # B只连接A
        [1, 0, 0],  # C只连接A
    ])
    
    print("节点特征:")
    print(node_features)
    print("\n邻接矩阵:")
    print(adjacency_matrix)
    
    # 创建GNN实例
    gnn = SimpleGNN(input_dim=2, hidden_dim=2)
    
    # 比较不同聚合方法
    methods = ['mean', 'sum', 'max']
    
    for method in methods:
        print(f"\n--- 使用 {method} 聚合 ---")
        aggregated = gnn.aggregate_neighbors(node_features, adjacency_matrix, method)
        print(f"聚合结果:")
        print(aggregated)


def visualize_gnn_process():
    """
    可视化GNN处理过程
    """
    print("\n📈 可视化GNN处理过程")
    print("=" * 40)
    
    # 创建数据
    node_features = np.array([
        [1.0, 0.5],
        [0.8, 1.2],
        [1.5, 0.3],
        [0.2, 1.8],
    ])
    
    adjacency_matrix = np.array([
        [0, 1, 0, 1],
        [1, 0, 1, 0],
        [0, 1, 0, 1],
        [1, 0, 1, 0],
    ])
    
    # 创建GNN并运行多轮
    gnn = SimpleGNN(input_dim=2, hidden_dim=2)
    
    # 存储每轮的特征
    features_history = [node_features.copy()]
    current_features = node_features.copy()
    
    # 运行3轮GNN
    for round_num in range(3):
        print(f"\n🔄 第 {round_num + 1} 轮 GNN")
        current_features = gnn.forward(current_features, adjacency_matrix)
        features_history.append(current_features.copy())
    
    # 可视化特征变化
    plt.figure(figsize=(15, 10))
    
    for i in range(4):  # 4个节点
        plt.subplot(2, 2, i + 1)
        
        # 提取每个节点的第一个特征随时间的变化
        feature_values = [features[i, 0] for features in features_history]
        
        plt.plot(range(len(feature_values)), feature_values, 'o-', linewidth=2, markersize=8)
        plt.title(f'节点 {i} 的特征变化', fontsize=14)
        plt.xlabel('GNN轮数')
        plt.ylabel('特征值')
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.suptitle('GNN多轮处理中节点特征的变化', fontsize=16, y=1.02)
    plt.show()
    
    return features_history


def understand_message_passing():
    """
    深入理解消息传递机制
    """
    print("\n🧠 深入理解消息传递")
    print("=" * 40)
    
    print("消息传递的三个核心步骤：")
    print("1. 📨 消息生成：每个节点向邻居发送消息")
    print("2. 📦 消息聚合：每个节点收集邻居的消息")
    print("3. 🔄 状态更新：基于收到的消息更新自己的状态")
    
    # 手动演示消息传递
    print("\n让我们手动演示一个简单的例子：")
    
    # 3个节点的线性图：A-B-C
    features = np.array([
        [1.0],  # 节点A
        [2.0],  # 节点B
        [3.0],  # 节点C
    ])
    
    adj = np.array([
        [0, 1, 0],  # A连接B
        [1, 0, 1],  # B连接A和C
        [0, 1, 0],  # C连接B
    ])
    
    print("\n初始状态:")
    print(f"节点A特征: {features[0]}")
    print(f"节点B特征: {features[1]}")
    print(f"节点C特征: {features[2]}")
    
    print("\n第一轮消息传递:")
    
    # 节点A收到B的消息
    print(f"节点A收到节点B的消息: {features[1]}")
    new_A = (features[0] + features[1]) / 2  # 简单平均
    print(f"节点A更新后: {new_A}")
    
    # 节点B收到A和C的消息
    print(f"节点B收到节点A的消息: {features[0]}")
    print(f"节点B收到节点C的消息: {features[2]}")
    new_B = (features[1] + features[0] + features[2]) / 3  # 简单平均
    print(f"节点B更新后: {new_B}")
    
    # 节点C收到B的消息
    print(f"节点C收到节点B的消息: {features[1]}")
    new_C = (features[2] + features[1]) / 2  # 简单平均
    print(f"节点C更新后: {new_C}")
    
    print("\n💡 观察：")
    print("- 节点的新特征融合了邻居的信息")
    print("- 信息在图中传播，节点间的特征变得更相似")
    print("- 这就是GNN学习图结构的方式！")


if __name__ == "__main__":
    print("🚀 欢迎来到GNN学习第三课！")
    print("今天我们手工实现第一个GNN\n")
    
    # 创建简单示例
    original_features, updated_features, adj_matrix = create_simple_example()
    
    # 比较聚合方法
    compare_aggregation_methods()
    
    # 理解消息传递
    understand_message_passing()
    
    # 可视化GNN过程
    features_history = visualize_gnn_process()
    
    print("\n🎉 恭喜！你已经理解了GNN的工作原理！")
    print("下一步我们将使用PyTorch Geometric框架实现更强大的GNN！")
