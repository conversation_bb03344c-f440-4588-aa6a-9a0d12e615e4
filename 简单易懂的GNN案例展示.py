"""
简单易懂的GNN学习成果展示
========================

这个案例用最简单的方式展示GNN的学习成果：
1. 创建一个小的社交网络
2. 训练GNN模型预测用户影响力
3. 用简单清晰的图表展示结果

重点：简单、清晰、易理解！
"""

import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv
from torch_geometric.data import Data
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def create_simple_social_network():
    """创建一个简单的社交网络"""
    print("🌐 创建简单社交网络...")
    
    # 只有20个用户，便于理解
    num_users = 20
    
    # 用户特征：[粉丝数, 发帖数, 活跃度]（简化为3个特征）
    np.random.seed(42)
    features = []
    
    for i in range(num_users):
        followers = np.random.randint(10, 1000)  # 粉丝数
        posts = np.random.randint(5, 100)       # 发帖数
        activity = np.random.uniform(0, 1)       # 活跃度
        features.append([followers, posts, activity])
    
    # 标准化特征
    features = np.array(features)
    features = (features - features.mean(axis=0)) / features.std(axis=0)
    x = torch.FloatTensor(features)
    
    # 创建简单的连接关系（手工设计，便于理解）
    edges = [
        [0, 1], [0, 2], [1, 3], [2, 4], [3, 5],  # 链式连接
        [4, 6], [5, 7], [6, 8], [7, 9], [8, 10], # 继续链式
        [9, 11], [10, 12], [11, 13], [12, 14],   # 更多连接
        [13, 15], [14, 16], [15, 17], [16, 18], [17, 19], # 到末尾
        [0, 10], [5, 15], [2, 12], [7, 17]       # 一些跨越连接
    ]
    
    edge_index = torch.LongTensor(edges).t().contiguous()
    
    # 简单的影响力标签：基于特征计算
    influence_score = x[:, 0] * 0.5 + x[:, 1] * 0.3 + x[:, 2] * 0.2
    
    # 分为3类：低(0)、中(1)、高(2)影响力
    y = torch.zeros(num_users, dtype=torch.long)
    sorted_scores = torch.sort(influence_score)[0]
    threshold_low = sorted_scores[6]   # 前1/3
    threshold_high = sorted_scores[13] # 前2/3
    
    y[influence_score >= threshold_high] = 2  # 高影响力
    y[(influence_score >= threshold_low) & (influence_score < threshold_high)] = 1  # 中影响力
    y[influence_score < threshold_low] = 0    # 低影响力
    
    # 训练/测试分割
    train_mask = torch.zeros(num_users, dtype=torch.bool)
    test_mask = torch.zeros(num_users, dtype=torch.bool)
    
    train_mask[:14] = True  # 前14个用户用于训练
    test_mask[14:] = True   # 后6个用户用于测试
    
    data = Data(x=x, edge_index=edge_index, y=y, train_mask=train_mask, test_mask=test_mask)
    
    print(f"✅ 网络创建完成:")
    print(f"  用户数量: {num_users}")
    print(f"  连接数量: {len(edges)}")
    print(f"  训练用户: {train_mask.sum()}")
    print(f"  测试用户: {test_mask.sum()}")
    print(f"  影响力分布: 低={torch.sum(y==0)}, 中={torch.sum(y==1)}, 高={torch.sum(y==2)}")
    
    return data


class SimpleGNN(torch.nn.Module):
    """简单的GNN模型"""
    
    def __init__(self, input_dim, hidden_dim, output_dim, model_type='GCN'):
        super(SimpleGNN, self).__init__()
        self.model_type = model_type
        
        if model_type == 'GCN':
            self.conv1 = GCNConv(input_dim, hidden_dim)
            self.conv2 = GCNConv(hidden_dim, output_dim)
        elif model_type == 'GAT':
            self.conv1 = GATConv(input_dim, hidden_dim, heads=2)
            self.conv2 = GATConv(hidden_dim * 2, output_dim, heads=1)
        elif model_type == 'SAGE':
            self.conv1 = SAGEConv(input_dim, hidden_dim)
            self.conv2 = SAGEConv(hidden_dim, output_dim)
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


def train_simple_model(model, data, epochs=100):
    """训练模型"""
    print(f"🚀 训练 {model.model_type} 模型...")
    
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
    criterion = torch.nn.NLLLoss()
    
    losses = []
    
    for epoch in range(epochs):
        model.train()
        optimizer.zero_grad()
        out = model(data.x, data.edge_index)
        loss = criterion(out[data.train_mask], data.y[data.train_mask])
        loss.backward()
        optimizer.step()
        
        losses.append(loss.item())
        
        if epoch % 20 == 0:
            train_acc = accuracy_score(
                data.y[data.train_mask].cpu(),
                out[data.train_mask].argmax(dim=1).cpu()
            )
            print(f'  Epoch {epoch}: Loss={loss:.3f}, 训练准确率={train_acc:.3f}')
    
    # 测试
    model.eval()
    with torch.no_grad():
        out = model(data.x, data.edge_index)
        test_pred = out[data.test_mask].argmax(dim=1)
        test_true = data.y[data.test_mask]
        test_acc = accuracy_score(test_true.cpu(), test_pred.cpu())
    
    print(f"✅ {model.model_type} 测试准确率: {test_acc:.3f}")
    
    return {
        'losses': losses,
        'test_accuracy': test_acc,
        'predictions': out,
        'test_pred': test_pred,
        'test_true': test_true
    }


def create_clear_visualization(data, results):
    """创建清晰易懂的可视化"""
    print("\n📊 创建简单清晰的结果展示...")
    
    # 创建2x2的图表布局
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. 模型准确率对比（左上）
    ax1 = axes[0, 0]
    models = list(results.keys())
    accuracies = [results[model]['test_accuracy'] for model in models]
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    bars = ax1.bar(models, accuracies, color=colors)
    
    # 在柱子上显示数值
    for bar, acc in zip(bars, accuracies):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{acc:.1%}', ha='center', va='bottom', fontsize=12, fontweight='bold')
    
    ax1.set_title('🏆 模型准确率对比', fontsize=14, fontweight='bold')
    ax1.set_ylabel('准确率')
    ax1.set_ylim(0, 1.1)
    ax1.grid(True, alpha=0.3)
    
    # 2. 训练过程（右上）
    ax2 = axes[0, 1]
    for i, (model_name, result) in enumerate(results.items()):
        ax2.plot(result['losses'], label=model_name, color=colors[i], linewidth=2)
    
    ax2.set_title('📈 训练损失变化', fontsize=14, fontweight='bold')
    ax2.set_xlabel('训练轮数')
    ax2.set_ylabel('损失值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 真实 vs 预测对比（左下）
    ax3 = axes[1, 0]
    
    # 选择最好的模型进行展示
    best_model = max(results.keys(), key=lambda x: results[x]['test_accuracy'])
    best_result = results[best_model]
    
    test_true = best_result['test_true'].cpu().numpy()
    test_pred = best_result['test_pred'].cpu().numpy()
    
    # 创建对比条形图
    x_pos = np.arange(len(test_true))
    width = 0.35
    
    ax3.bar(x_pos - width/2, test_true, width, label='真实标签', color='lightblue', alpha=0.8)
    ax3.bar(x_pos + width/2, test_pred, width, label='预测标签', color='orange', alpha=0.8)
    
    ax3.set_title(f'🎯 {best_model} 预测结果对比', fontsize=14, fontweight='bold')
    ax3.set_xlabel('测试用户')
    ax3.set_ylabel('影响力等级')
    ax3.set_xticks(x_pos)
    ax3.set_xticklabels([f'用户{i+15}' for i in range(len(test_true))])
    ax3.legend()
    ax3.set_yticks([0, 1, 2])
    ax3.set_yticklabels(['低', '中', '高'])
    
    # 4. 影响力分布（右下）
    ax4 = axes[1, 1]
    
    influence_counts = torch.bincount(data.y)
    labels = ['低影响力', '中影响力', '高影响力']
    colors_pie = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    wedges, texts, autotexts = ax4.pie(influence_counts, labels=labels, colors=colors_pie,
                                      autopct='%1.0f%%', startangle=90, textprops={'fontsize': 10})
    
    ax4.set_title('📊 用户影响力分布', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('简单易懂的GNN学习成果.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 可视化图表已保存为 '简单易懂的GNN学习成果.png'")


def print_simple_results(data, results):
    """打印简单易懂的结果"""
    print(f"\n🎉 学习成果总结")
    print("=" * 50)
    
    print(f"📊 数据概况:")
    print(f"  • 总用户数: {data.num_nodes}")
    print(f"  • 连接数: {data.num_edges}")
    print(f"  • 训练用户: {data.train_mask.sum()}")
    print(f"  • 测试用户: {data.test_mask.sum()}")
    
    print(f"\n🏆 模型性能:")
    for model_name, result in results.items():
        print(f"  • {model_name}: {result['test_accuracy']:.1%}")
    
    best_model = max(results.keys(), key=lambda x: results[x]['test_accuracy'])
    print(f"\n🥇 最佳模型: {best_model} ({results[best_model]['test_accuracy']:.1%})")
    
    print(f"\n✨ 这个案例展示了:")
    print(f"  ✓ 如何创建图数据")
    print(f"  ✓ 如何训练GNN模型")
    print(f"  ✓ 如何比较不同模型")
    print(f"  ✓ 如何可视化结果")


def main():
    """主函数 - 简单易懂的GNN展示"""
    print("🎓 简单易懂的GNN学习成果展示")
    print("=" * 50)
    print("用最简单的方式展示GNN的强大能力！\n")
    
    # 1. 创建数据
    data = create_simple_social_network()
    
    # 2. 训练三种模型
    print(f"\n🤖 训练不同的GNN模型")
    print("-" * 30)
    
    models = {
        'GCN': SimpleGNN(3, 8, 3, 'GCN'),
        'GAT': SimpleGNN(3, 4, 3, 'GAT'),
        'GraphSAGE': SimpleGNN(3, 8, 3, 'SAGE')
    }
    
    results = {}
    for name, model in models.items():
        result = train_simple_model(model, data, epochs=80)
        results[name] = result
    
    # 3. 可视化结果
    create_clear_visualization(data, results)
    
    # 4. 打印总结
    print_simple_results(data, results)


if __name__ == "__main__":
    main()
