"""
GNN学习第四课：PyTorch Geometric入门 - 使用本地生成数据
====================================================

由于网络问题无法下载Cora数据集，我们使用本地生成的数据来学习PyG的使用方法。
这样可以更好地理解数据格式和模型训练过程。
"""

import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv
from torch_geometric.data import Data
import matplotlib.pyplot as plt
import numpy as np
import networkx as nx
from sklearn.datasets import make_classification

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def create_synthetic_graph_data(num_nodes=1000, num_features=10, num_classes=3):
    """
    创建合成图数据
    """
    print(f"🔧 创建合成图数据...")
    print(f"  节点数: {num_nodes}")
    print(f"  特征维度: {num_features}")
    print(f"  类别数: {num_classes}")
    
    # 生成节点特征和标签
    X, y = make_classification(
        n_samples=num_nodes,
        n_features=num_features,
        n_classes=num_classes,
        n_informative=num_features//2,
        n_redundant=0,
        n_clusters_per_class=1,
        random_state=42
    )
    
    # 转换为PyTorch张量
    x = torch.FloatTensor(X)
    y = torch.LongTensor(y)
    
    # 创建图结构（基于特征相似性）
    print("  创建图结构...")
    
    # 计算节点间的相似性
    similarity_matrix = torch.mm(F.normalize(x, dim=1), F.normalize(x, dim=1).t())
    
    # 为每个节点连接最相似的k个节点
    k = 10  # 每个节点的平均度数
    edge_list = []
    
    for i in range(num_nodes):
        # 找到与节点i最相似的k个节点
        similarities = similarity_matrix[i]
        _, top_k_indices = torch.topk(similarities, k + 1)  # +1因为包含自己
        
        for j in top_k_indices[1:]:  # 排除自己
            if i < j:  # 避免重复边
                edge_list.append([i, j.item()])
                edge_list.append([j.item(), i])  # 无向图
    
    # 转换为edge_index格式
    edge_index = torch.LongTensor(edge_list).t().contiguous()
    
    # 创建训练/验证/测试掩码
    num_train = int(0.6 * num_nodes)
    num_val = int(0.2 * num_nodes)
    
    train_mask = torch.zeros(num_nodes, dtype=torch.bool)
    val_mask = torch.zeros(num_nodes, dtype=torch.bool)
    test_mask = torch.zeros(num_nodes, dtype=torch.bool)
    
    train_mask[:num_train] = True
    val_mask[num_train:num_train + num_val] = True
    test_mask[num_train + num_val:] = True
    
    # 创建PyG数据对象
    data = Data(x=x, edge_index=edge_index, y=y,
                train_mask=train_mask, val_mask=val_mask, test_mask=test_mask)
    
    print(f"✅ 图数据创建完成:")
    print(f"  节点数: {data.num_nodes}")
    print(f"  边数: {data.num_edges}")
    print(f"  特征维度: {data.num_node_features}")
    print(f"  训练节点: {train_mask.sum()}")
    print(f"  验证节点: {val_mask.sum()}")
    print(f"  测试节点: {test_mask.sum()}")
    
    return data, num_classes


class GCN(torch.nn.Module):
    """图卷积网络"""
    
    def __init__(self, input_dim, hidden_dim, output_dim, dropout=0.5):
        super(GCN, self).__init__()
        self.conv1 = GCNConv(input_dim, hidden_dim)
        self.conv2 = GCNConv(hidden_dim, output_dim)
        self.dropout = dropout
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=self.dropout, training=self.training)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


class GAT(torch.nn.Module):
    """图注意力网络"""
    
    def __init__(self, input_dim, hidden_dim, output_dim, heads=8, dropout=0.5):
        super(GAT, self).__init__()
        self.conv1 = GATConv(input_dim, hidden_dim, heads=heads, dropout=dropout)
        self.conv2 = GATConv(hidden_dim * heads, output_dim, heads=1, dropout=dropout)
        self.dropout = dropout
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=self.dropout, training=self.training)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


class GraphSAGE(torch.nn.Module):
    """GraphSAGE网络"""
    
    def __init__(self, input_dim, hidden_dim, output_dim, dropout=0.5):
        super(GraphSAGE, self).__init__()
        self.conv1 = SAGEConv(input_dim, hidden_dim)
        self.conv2 = SAGEConv(hidden_dim, output_dim)
        self.dropout = dropout
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=self.dropout, training=self.training)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


def train_model(model, data, epochs=200, lr=0.01):
    """训练模型"""
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=5e-4)
    criterion = torch.nn.NLLLoss()
    
    train_losses = []
    train_accs = []
    val_accs = []
    
    model.train()
    
    for epoch in range(epochs):
        optimizer.zero_grad()
        out = model(data.x, data.edge_index)
        loss = criterion(out[data.train_mask], data.y[data.train_mask])
        loss.backward()
        optimizer.step()
        
        # 计算准确率
        train_acc = accuracy(out[data.train_mask], data.y[data.train_mask])
        val_acc = accuracy(out[data.val_mask], data.y[data.val_mask])
        
        train_losses.append(loss.item())
        train_accs.append(train_acc)
        val_accs.append(val_acc)
        
        if epoch % 50 == 0:
            print(f'Epoch {epoch:03d}: Loss={loss:.4f}, Train Acc={train_acc:.4f}, Val Acc={val_acc:.4f}')
    
    return train_losses, train_accs, val_accs


def test_model(model, data):
    """测试模型"""
    model.eval()
    with torch.no_grad():
        out = model(data.x, data.edge_index)
        test_acc = accuracy(out[data.test_mask], data.y[data.test_mask])
    return test_acc


def accuracy(pred, target):
    """计算准确率"""
    pred_class = pred.argmax(dim=1)
    return (pred_class == target).float().mean().item()


def compare_models(data, num_classes):
    """比较不同的GNN模型"""
    print("\n🏆 比较不同GNN模型性能")
    print("=" * 50)
    
    models = {
        'GCN': GCN(data.num_node_features, 16, num_classes),
        'GAT': GAT(data.num_node_features, 8, num_classes, heads=8),
        'GraphSAGE': GraphSAGE(data.num_node_features, 16, num_classes)
    }
    
    results = {}
    
    for name, model in models.items():
        print(f"\n训练 {name} 模型...")
        
        # 设置随机种子确保公平比较
        torch.manual_seed(42)
        
        # 训练模型
        train_losses, train_accs, val_accs = train_model(model, data, epochs=200)
        
        # 测试模型
        test_acc = test_model(model, data)
        
        results[name] = {
            'test_acc': test_acc,
            'train_losses': train_losses,
            'train_accs': train_accs,
            'val_accs': val_accs
        }
        
        print(f"{name} 测试准确率: {test_acc:.4f}")
    
    return results


def visualize_results(results):
    """可视化结果"""
    print("\n📊 可视化训练结果...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 绘制损失曲线
    ax1 = axes[0, 0]
    for name, result in results.items():
        ax1.plot(result['train_losses'], label=f'{name}')
    ax1.set_title('训练损失对比', fontsize=14)
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 绘制训练准确率
    ax2 = axes[0, 1]
    for name, result in results.items():
        ax2.plot(result['train_accs'], label=f'{name}')
    ax2.set_title('训练准确率对比', fontsize=14)
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 绘制验证准确率
    ax3 = axes[1, 0]
    for name, result in results.items():
        ax3.plot(result['val_accs'], label=f'{name}')
    ax3.set_title('验证准确率对比', fontsize=14)
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Accuracy')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 绘制最终测试准确率对比
    ax4 = axes[1, 1]
    names = list(results.keys())
    test_accs = [results[name]['test_acc'] for name in names]
    bars = ax4.bar(names, test_accs, color=['skyblue', 'lightgreen', 'lightcoral'])
    ax4.set_title('测试准确率对比', fontsize=14)
    ax4.set_ylabel('Test Accuracy')
    
    # 在柱状图上添加数值
    for bar, acc in zip(bars, test_accs):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{acc:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()


def analyze_graph_properties(data):
    """分析图的性质"""
    print("\n🔍 分析图的性质...")
    
    # 转换为NetworkX图进行分析
    edge_list = data.edge_index.t().numpy()
    G = nx.Graph()
    G.add_edges_from(edge_list)
    
    # 计算图的基本性质
    print(f"图的基本性质:")
    print(f"  节点数: {G.number_of_nodes()}")
    print(f"  边数: {G.number_of_edges()}")
    print(f"  平均度数: {2 * G.number_of_edges() / G.number_of_nodes():.2f}")
    print(f"  连通分量数: {nx.number_connected_components(G)}")
    
    # 度数分布
    degrees = [d for n, d in G.degree()]
    
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.hist(degrees, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title('度数分布', fontsize=14)
    plt.xlabel('度数')
    plt.ylabel('节点数量')
    plt.grid(True, alpha=0.3)
    
    # 特征分布
    plt.subplot(1, 2, 2)
    feature_means = data.x.mean(dim=1).numpy()
    plt.hist(feature_means, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
    plt.title('节点特征均值分布', fontsize=14)
    plt.xlabel('特征均值')
    plt.ylabel('节点数量')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    print("🚀 欢迎来到GNN学习第四课（本地数据版）！")
    print("我们将使用合成数据学习PyTorch Geometric的使用方法\n")
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 创建合成图数据
    data, num_classes = create_synthetic_graph_data(num_nodes=1000, num_features=10, num_classes=3)
    
    # 分析图性质
    analyze_graph_properties(data)
    
    # 比较不同模型
    results = compare_models(data, num_classes)
    
    # 可视化结果
    visualize_results(results)
    
    # 总结结果
    print("\n🎯 实验结果总结:")
    print("=" * 40)
    for name, result in results.items():
        print(f"{name:>10}: {result['test_acc']:.4f}")
    
    best_model = max(results.keys(), key=lambda x: results[x]['test_acc'])
    print(f"\n🏆 最佳模型: {best_model} (准确率: {results[best_model]['test_acc']:.4f})")
    
    print("\n🎉 恭喜！你已经成功使用PyTorch Geometric实现并比较了多种GNN模型！")
    print("下一步我们将学习更多高级的GNN技术和应用！")
