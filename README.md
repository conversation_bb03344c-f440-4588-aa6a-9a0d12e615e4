# GNN学习项目 🚀

欢迎来到图神经网络（Graph Neural Networks）的学习之旅！这是一个完整的GNN学习项目，从基础概念到实际应用，帮助你系统掌握图神经网络。

## 🎉 项目特色

- **循序渐进**：从零基础到深入理解
- **理论实践结合**：每个概念都有代码实现
- **完整可运行**：所有代码都经过测试
- **中文友好**：详细的中文注释和说明

## 📚 学习路径

### ✅ 第一阶段：基础概念
- [x] **环境搭建** - 安装所需依赖包
- [x] **图的基本概念** - 节点、边、邻接矩阵
- [x] **节点特征和图数据** - 图数据的表示方法

### ✅ 第二阶段：核心原理
- [x] **手工实现简单GNN** - 理解消息传递机制
- [x] **PyTorch Geometric入门** - 现代GNN框架使用

### ✅ 第三阶段：进阶技术
- [x] **消息传递策略详解** - GCN、GAT、GraphSAGE对比
- [x] **自定义GNN层** - 深入理解框架底层

### 🔄 第四阶段：实际应用（进行中）
- [ ] 真实数据集实验
- [ ] 不同应用场景探索
- [ ] 性能优化技巧

## 🛠️ 环境要求

- Python 3.8+
- PyTorch 2.0+
- PyTorch Geometric 2.3+
- NumPy, Matplotlib, NetworkX
- scikit-learn

## 📁 项目结构

```
gnn实验/
├── README.md                           # 项目说明
├── requirements.txt                    # 依赖包列表
├── install_dependencies.py            # 自动安装脚本
├── 运行所有示例.py                     # 一键运行所有示例
├── 学习总结和下一步.md                 # 学习指南
├── 01_基础概念/                       # 基础概念学习
│   ├── 01_图的基本概念.py             # 图、节点、边的概念
│   └── 02_节点特征和图数据.py         # 图数据表示方法
├── 02_手工实现/                       # 手工实现GNN
│   └── 01_手工实现简单GNN.py          # 从零实现消息传递
├── 03_框架学习/                       # PyG框架学习
│   ├── 01_PyG入门_GCN实现.py          # 原始版本（需网络）
│   ├── 02_PyG入门_本地数据.py         # 本地数据版本
│   └── 03_PyG核心学习.py              # 核心概念学习
├── 04_进阶应用/                       # 进阶应用
│   └── 01_消息传递策略详解.py         # 深入理解不同策略
└── 数据集/                            # 实验数据存放
```

## 🚀 快速开始

### 1. 安装依赖
```bash
# 自动安装所有依赖
python install_dependencies.py

# 或手动安装
pip install -r requirements.txt
```

### 2. 运行学习示例
```bash
# 一键运行所有示例（推荐）
python 运行所有示例.py

# 或逐个运行
python "01_基础概念/01_图的基本概念.py"
python "01_基础概念/02_节点特征和图数据.py"
python "02_手工实现/01_手工实现简单GNN.py"
python "03_框架学习/03_PyG核心学习.py"
python "04_进阶应用/01_消息传递策略详解.py"
```

### 3. 查看学习指南
```bash
# 查看详细的学习总结和下一步建议
cat 学习总结和下一步.md
```

## 🎯 学习目标

通过这个项目，你将：

### 🧠 理论理解
- 掌握图神经网络的核心概念
- 理解消息传递机制的工作原理
- 了解不同GNN方法的优缺点

### 💻 实践技能
- 能够手工实现简单的GNN
- 熟练使用PyTorch Geometric框架
- 可以在新数据集上应用GNN

### 🔧 工程能力
- 掌握GNN模型的训练和调试
- 了解性能优化的基本方法
- 具备解决实际问题的能力

## 📖 核心概念速览

### 消息传递三步骤
```
1. 📨 消息生成：邻居向节点发送消息
2. 📦 消息聚合：节点收集并整合消息
3. 🔄 状态更新：基于消息更新节点特征
```

### 三大经典方法
- **GCN**：基于谱图理论的图卷积
- **GAT**：引入注意力机制的图网络
- **GraphSAGE**：支持大规模图的采样聚合

让我们开始这个激动人心的学习之旅吧！🌟
