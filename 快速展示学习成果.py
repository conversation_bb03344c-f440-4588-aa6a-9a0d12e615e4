"""
快速展示GNN学习成果
==================

这个脚本用最简单的方式展示你的GNN学习成果，
重点突出关键结果，避免复杂的可视化。
"""

import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv
from torch_geometric.data import Data
import numpy as np

def create_demo_data():
    """创建演示数据"""
    print("🌐 创建演示社交网络...")
    
    # 10个用户的小网络
    num_users = 10
    
    # 用户特征：[粉丝数, 发帖数, 活跃度]
    features = [
        [100, 20, 0.8],   # 用户0: 高活跃
        [50, 10, 0.4],    # 用户1: 中等
        [200, 40, 0.9],   # 用户2: 很活跃
        [30, 5, 0.2],     # 用户3: 低活跃
        [150, 30, 0.7],   # 用户4: 高活跃
        [80, 15, 0.5],    # 用户5: 中等
        [300, 60, 1.0],   # 用户6: 超级活跃
        [40, 8, 0.3],     # 用户7: 低活跃
        [120, 25, 0.6],   # 用户8: 中等
        [60, 12, 0.4],    # 用户9: 中等
    ]
    
    # 标准化
    features = np.array(features)
    features = (features - features.mean(axis=0)) / features.std(axis=0)
    x = torch.FloatTensor(features)
    
    # 连接关系
    edges = [
        [0, 1], [0, 2], [1, 3], [2, 4], [2, 6],  # 从活跃用户出发
        [4, 5], [6, 7], [6, 8], [8, 9], [5, 9]   # 形成网络
    ]
    edge_index = torch.LongTensor(edges).t().contiguous()
    
    # 影响力标签（基于特征计算）
    influence_score = x[:, 0] * 0.4 + x[:, 1] * 0.3 + x[:, 2] * 0.3
    y = torch.zeros(num_users, dtype=torch.long)
    
    # 分为3类
    high_threshold = torch.quantile(influence_score, 0.7)
    low_threshold = torch.quantile(influence_score, 0.3)
    
    y[influence_score >= high_threshold] = 2  # 高影响力
    y[(influence_score >= low_threshold) & (influence_score < high_threshold)] = 1  # 中影响力
    y[influence_score < low_threshold] = 0    # 低影响力
    
    # 训练/测试分割
    train_mask = torch.BoolTensor([True, True, True, True, True, True, False, False, False, False])
    test_mask = torch.BoolTensor([False, False, False, False, False, False, True, True, True, True])
    
    data = Data(x=x, edge_index=edge_index, y=y, train_mask=train_mask, test_mask=test_mask)
    
    print(f"✅ 数据创建完成: {num_users}个用户, {len(edges)}个连接")
    return data


class DemoGNN(torch.nn.Module):
    """演示用的简单GNN"""
    
    def __init__(self, model_type='GraphSAGE'):
        super(DemoGNN, self).__init__()
        self.model_type = model_type
        
        if model_type == 'GraphSAGE':
            self.conv1 = SAGEConv(3, 8)
            self.conv2 = SAGEConv(8, 3)
        elif model_type == 'GAT':
            self.conv1 = GATConv(3, 4, heads=2)
            self.conv2 = GATConv(8, 3, heads=1)
        else:  # GCN
            self.conv1 = GCNConv(3, 8)
            self.conv2 = GCNConv(8, 3)
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


def quick_train(model, data, epochs=50):
    """快速训练模型"""
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
    criterion = torch.nn.NLLLoss()
    
    print(f"🚀 训练 {model.model_type} 模型...")
    
    for epoch in range(epochs):
        model.train()
        optimizer.zero_grad()
        out = model(data.x, data.edge_index)
        loss = criterion(out[data.train_mask], data.y[data.train_mask])
        loss.backward()
        optimizer.step()
        
        if epoch % 10 == 0:
            train_acc = (out[data.train_mask].argmax(dim=1) == data.y[data.train_mask]).float().mean()
            print(f"  Epoch {epoch}: Loss={loss:.3f}, 训练准确率={train_acc:.3f}")
    
    # 测试
    model.eval()
    with torch.no_grad():
        out = model(data.x, data.edge_index)
        test_pred = out[data.test_mask].argmax(dim=1)
        test_true = data.y[data.test_mask]
        test_acc = (test_pred == test_true).float().mean()
    
    return test_acc.item(), test_pred, test_true


def show_predictions(data, pred, true):
    """展示预测结果"""
    print(f"\n🎯 预测结果对比:")
    print("用户ID | 真实影响力 | 预测影响力 | 是否正确")
    print("-" * 45)
    
    influence_names = ['低', '中', '高']
    test_indices = torch.where(data.test_mask)[0]
    
    correct = 0
    for i, (user_id, t, p) in enumerate(zip(test_indices, true, pred)):
        is_correct = "✅" if t == p else "❌"
        if t == p:
            correct += 1
        print(f"用户{user_id.item():2d}  |     {influence_names[t]}     |     {influence_names[p]}     |    {is_correct}")
    
    print(f"\n准确率: {correct}/{len(true)} = {correct/len(true):.1%}")


def main():
    """主演示函数"""
    print("🎓 GNN学习成果快速展示")
    print("=" * 40)
    print("用最简单的方式展示GNN的强大能力！\n")
    
    # 1. 创建数据
    data = create_demo_data()
    
    # 2. 展示数据信息
    print(f"\n📊 数据概况:")
    print(f"  • 用户特征: 粉丝数、发帖数、活跃度")
    print(f"  • 影响力分布: 低={torch.sum(data.y==0)}人, 中={torch.sum(data.y==1)}人, 高={torch.sum(data.y==2)}人")
    print(f"  • 训练用户: {data.train_mask.sum()}人")
    print(f"  • 测试用户: {data.test_mask.sum()}人")
    
    # 3. 训练最佳模型
    print(f"\n🤖 使用最佳模型 GraphSAGE:")
    model = DemoGNN('GraphSAGE')
    test_acc, pred, true = quick_train(model, data, epochs=60)
    
    print(f"\n✅ 最终测试准确率: {test_acc:.1%}")
    
    # 4. 展示预测结果
    show_predictions(data, pred, true)
    
    # 5. 总结
    print(f"\n🎉 学习成果总结:")
    print(f"✓ 成功创建了图神经网络模型")
    print(f"✓ 在社交网络影响力预测任务上达到 {test_acc:.1%} 准确率")
    print(f"✓ 理解了GNN如何结合用户特征和网络结构")
    print(f"✓ 掌握了完整的图学习项目流程")
    
    print(f"\n💡 这个案例展示了:")
    print(f"  🔹 图数据的创建和处理")
    print(f"  🔹 GNN模型的训练和评估") 
    print(f"  🔹 实际问题的解决能力")
    print(f"  🔹 从理论到实践的完整掌握")
    
    print(f"\n🌟 恭喜！你已经成功掌握了图神经网络！")


if __name__ == "__main__":
    main()
