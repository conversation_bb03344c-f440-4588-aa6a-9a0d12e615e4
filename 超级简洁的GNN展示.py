"""
超级简洁的GNN学习成果展示
========================

这次我们只展示最重要的结果，用最简洁清晰的方式。
不要复杂的网络图，只要核心的性能对比！
"""

import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv
from torch_geometric.data import Data
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')  # 使用默认样式，更简洁

def create_simple_data():
    """创建简单的演示数据"""
    print("🌐 创建简单社交网络...")
    
    # 只有15个用户，便于理解
    num_users = 15
    
    # 简单的用户特征：[粉丝数, 活跃度, 认证状态]
    np.random.seed(42)
    features = []
    
    for i in range(num_users):
        followers = np.random.randint(10, 500)  # 粉丝数
        activity = np.random.uniform(0, 1)       # 活跃度
        verified = np.random.choice([0, 1], p=[0.8, 0.2])  # 认证状态
        features.append([followers, activity, verified])
    
    # 标准化
    features = np.array(features)
    features = (features - features.mean(axis=0)) / (features.std(axis=0) + 1e-8)
    x = torch.FloatTensor(features)
    
    # 简单的连接关系
    edges = [
        [0, 1], [1, 2], [2, 3], [3, 4],  # 链式连接
        [0, 5], [5, 6], [6, 7],          # 分支1
        [0, 8], [8, 9], [9, 10],         # 分支2
        [4, 11], [11, 12],               # 分支3
        [7, 13], [10, 14],               # 跨连接
        [2, 8], [6, 11]                  # 更多连接
    ]
    
    edge_index = torch.LongTensor(edges).t().contiguous()
    
    # 影响力标签（基于特征）
    influence_score = x[:, 0] * 0.5 + x[:, 1] * 0.3 + x[:, 2] * 0.2
    y = torch.zeros(num_users, dtype=torch.long)
    
    # 分为3类
    high_threshold = torch.quantile(influence_score, 0.67)
    low_threshold = torch.quantile(influence_score, 0.33)
    
    y[influence_score >= high_threshold] = 2  # 高影响力
    y[(influence_score >= low_threshold) & (influence_score < high_threshold)] = 1  # 中影响力
    y[influence_score < low_threshold] = 0    # 低影响力
    
    # 训练/测试分割
    train_mask = torch.zeros(num_users, dtype=torch.bool)
    test_mask = torch.zeros(num_users, dtype=torch.bool)
    
    train_mask[:10] = True  # 前10个用于训练
    test_mask[10:] = True   # 后5个用于测试
    
    data = Data(x=x, edge_index=edge_index, y=y, train_mask=train_mask, test_mask=test_mask)
    
    print(f"✅ 数据创建完成: {num_users}个用户, {len(edges)}个连接")
    print(f"   训练用户: {train_mask.sum()}, 测试用户: {test_mask.sum()}")
    print(f"   影响力分布: 低={torch.sum(y==0)}, 中={torch.sum(y==1)}, 高={torch.sum(y==2)}")
    
    return data


class SimpleGNN(torch.nn.Module):
    """简单的GNN模型"""
    
    def __init__(self, model_type='GCN'):
        super(SimpleGNN, self).__init__()
        self.model_type = model_type
        
        if model_type == 'GCN':
            self.conv1 = GCNConv(3, 8)
            self.conv2 = GCNConv(8, 3)
        elif model_type == 'GAT':
            self.conv1 = GATConv(3, 4, heads=2)
            self.conv2 = GATConv(8, 3, heads=1)
        elif model_type == 'GraphSAGE':
            self.conv1 = SAGEConv(3, 8)
            self.conv2 = SAGEConv(8, 3)
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=0.3, training=self.training)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


def train_model(model, data, epochs=80):
    """训练模型"""
    print(f"🚀 训练 {model.model_type} 模型...")
    
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
    criterion = torch.nn.NLLLoss()
    
    losses = []
    
    for epoch in range(epochs):
        model.train()
        optimizer.zero_grad()
        out = model(data.x, data.edge_index)
        loss = criterion(out[data.train_mask], data.y[data.train_mask])
        loss.backward()
        optimizer.step()
        
        losses.append(loss.item())
        
        if epoch % 20 == 0:
            train_acc = accuracy_score(
                data.y[data.train_mask].cpu(),
                out[data.train_mask].argmax(dim=1).cpu()
            )
            print(f"  Epoch {epoch}: Loss={loss:.3f}, 训练准确率={train_acc:.3f}")
    
    # 测试
    model.eval()
    with torch.no_grad():
        out = model(data.x, data.edge_index)
        test_pred = out[data.test_mask].argmax(dim=1)
        test_true = data.y[data.test_mask]
        test_acc = accuracy_score(test_true.cpu(), test_pred.cpu())
    
    print(f"✅ {model.model_type} 测试准确率: {test_acc:.1%}")
    
    return {
        'test_accuracy': test_acc,
        'losses': losses,
        'predictions': test_pred,
        'true_labels': test_true
    }


def create_super_clean_visualization(results, data):
    """创建超级简洁的可视化"""
    print("\n📊 创建超级简洁的可视化...")
    
    # 只创建2个图：性能对比 + 训练过程
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle('GNN学习成果展示', fontsize=16, fontweight='bold')
    
    # 1. 模型性能对比（左图）
    models = list(results.keys())
    accuracies = [results[model]['test_accuracy'] for model in models]
    
    # 使用渐变色
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    bars = ax1.bar(models, accuracies, color=colors, alpha=0.8, 
                   edgecolor='white', linewidth=2)
    
    # 添加数值标签
    for bar, acc in zip(bars, accuracies):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{acc:.0%}', ha='center', va='bottom', 
                fontweight='bold', fontsize=14)
    
    ax1.set_title('模型准确率对比', fontsize=14, fontweight='bold', pad=20)
    ax1.set_ylabel('测试准确率', fontsize=12)
    ax1.set_ylim(0, 1.1)
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 美化
    ax1.spines['top'].set_visible(False)
    ax1.spines['right'].set_visible(False)
    
    # 2. 训练损失对比（右图）
    for i, (model_name, result) in enumerate(results.items()):
        ax2.plot(result['losses'], label=model_name, 
                color=colors[i], linewidth=3, alpha=0.8)
    
    ax2.set_title('训练损失变化', fontsize=14, fontweight='bold', pad=20)
    ax2.set_xlabel('训练轮数', fontsize=12)
    ax2.set_ylabel('损失值', fontsize=12)
    ax2.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax2.grid(True, alpha=0.3)
    
    # 美化
    ax2.spines['top'].set_visible(False)
    ax2.spines['right'].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('超级简洁的GNN学习成果.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 超级简洁版图表已保存为 '超级简洁的GNN学习成果.png'")


def show_detailed_results(results, data):
    """显示详细的文字结果"""
    print(f"\n🎯 详细结果分析")
    print("=" * 50)
    
    # 找到最佳模型
    best_model = max(results.keys(), key=lambda x: results[x]['test_accuracy'])
    best_acc = results[best_model]['test_accuracy']
    
    print(f"📊 数据概况:")
    print(f"  • 用户数量: {data.num_nodes}")
    print(f"  • 连接数量: {data.num_edges}")
    print(f"  • 特征维度: {data.num_node_features}")
    
    print(f"\n🏆 模型性能排名:")
    sorted_results = sorted(results.items(), key=lambda x: x[1]['test_accuracy'], reverse=True)
    
    for i, (model, result) in enumerate(sorted_results, 1):
        emoji = "🥇" if i == 1 else "🥈" if i == 2 else "🥉"
        print(f"  {emoji} {model}: {result['test_accuracy']:.1%}")
    
    print(f"\n🎯 最佳模型详情:")
    print(f"  • 模型: {best_model}")
    print(f"  • 准确率: {best_acc:.1%}")
    
    # 显示预测结果
    best_result = results[best_model]
    pred = best_result['predictions']
    true = best_result['true_labels']
    
    print(f"\n📋 测试集预测结果:")
    influence_names = ['低影响力', '中影响力', '高影响力']
    
    correct = 0
    for i, (t, p) in enumerate(zip(true, pred)):
        status = "✅" if t == p else "❌"
        if t == p:
            correct += 1
        print(f"  用户{i+11}: 真实={influence_names[t]} | 预测={influence_names[p]} {status}")
    
    print(f"\n🎉 最终成果:")
    print(f"  ✓ 成功训练了3种GNN模型")
    print(f"  ✓ 最佳模型达到 {best_acc:.0%} 准确率")
    print(f"  ✓ 正确预测了 {correct}/{len(true)} 个测试样本")
    print(f"  ✓ 完成了完整的图学习项目")


def main():
    """主函数"""
    print("🎓 超级简洁的GNN学习成果展示")
    print("=" * 50)
    print("这次只展示最重要的结果，简洁明了！\n")
    
    # 1. 创建数据
    data = create_simple_data()
    
    # 2. 训练模型
    print(f"\n🤖 训练三种GNN模型")
    print("-" * 30)
    
    models = ['GCN', 'GAT', 'GraphSAGE']
    results = {}
    
    for model_name in models:
        model = SimpleGNN(model_name)
        result = train_model(model, data, epochs=60)
        results[model_name] = result
    
    # 3. 创建简洁可视化
    create_super_clean_visualization(results, data)
    
    # 4. 显示详细结果
    show_detailed_results(results, data)


if __name__ == "__main__":
    main()
