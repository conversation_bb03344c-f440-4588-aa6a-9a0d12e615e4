"""
GNN学习第一课：图的基本概念
=========================

在这个文件中，我们将学习：
1. 什么是图（Graph）
2. 如何用代码表示图
3. 邻接矩阵的概念
4. 图的可视化

让我们从最简单的例子开始！
"""

import numpy as np
import matplotlib.pyplot as plt
import networkx as nx

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SimpleGraph:
    """
    一个简单的图类，帮助理解图的基本概念
    """
    
    def __init__(self):
        self.nodes = []  # 存储节点
        self.edges = []  # 存储边
        self.node_features = {}  # 存储节点特征
    
    def add_node(self, node_id, features=None):
        """添加节点"""
        if node_id not in self.nodes:
            self.nodes.append(node_id)
            if features is not None:
                self.node_features[node_id] = features
            print(f"添加节点: {node_id}")
    
    def add_edge(self, node1, node2):
        """添加边（无向图）"""
        if node1 in self.nodes and node2 in self.nodes:
            edge = (node1, node2)
            if edge not in self.edges and (node2, node1) not in self.edges:
                self.edges.append(edge)
                print(f"添加边: {node1} -- {node2}")
        else:
            print("错误：节点不存在！")
    
    def get_adjacency_matrix(self):
        """获取邻接矩阵"""
        n = len(self.nodes)
        # 创建节点到索引的映射
        node_to_idx = {node: i for i, node in enumerate(self.nodes)}
        
        # 初始化邻接矩阵
        adj_matrix = np.zeros((n, n), dtype=int)
        
        # 填充邻接矩阵
        for node1, node2 in self.edges:
            i, j = node_to_idx[node1], node_to_idx[node2]
            adj_matrix[i][j] = 1
            adj_matrix[j][i] = 1  # 无向图是对称的
        
        return adj_matrix, node_to_idx
    
    def visualize(self):
        """可视化图"""
        # 使用NetworkX进行可视化
        G = nx.Graph()
        G.add_nodes_from(self.nodes)
        G.add_edges_from(self.edges)
        
        plt.figure(figsize=(10, 6))
        
        # 绘制图
        pos = nx.spring_layout(G)
        nx.draw(G, pos, with_labels=True, node_color='lightblue', 
                node_size=1000, font_size=16, font_weight='bold')
        
        plt.title("我们的第一个图", fontsize=16)
        plt.show()
    
    def print_info(self):
        """打印图的基本信息"""
        print("\n=== 图的基本信息 ===")
        print(f"节点数量: {len(self.nodes)}")
        print(f"边数量: {len(self.edges)}")
        print(f"节点列表: {self.nodes}")
        print(f"边列表: {self.edges}")


def create_social_network_example():
    """
    创建一个社交网络的例子
    想象这是一个小班级的朋友关系网络
    """
    print("🌟 创建社交网络例子")
    print("=" * 40)
    
    # 创建图
    social_graph = SimpleGraph()
    
    # 添加学生（节点）
    students = ["小明", "小红", "小刚", "小丽", "小华"]
    for student in students:
        social_graph.add_node(student)
    
    print("\n添加朋友关系（边）:")
    # 添加朋友关系（边）
    friendships = [
        ("小明", "小红"),  # 小明和小红是朋友
        ("小明", "小刚"),  # 小明和小刚是朋友
        ("小红", "小丽"),  # 小红和小丽是朋友
        ("小刚", "小华"),  # 小刚和小华是朋友
        ("小丽", "小华"),  # 小丽和小华是朋友
    ]
    
    for friend1, friend2 in friendships:
        social_graph.add_edge(friend1, friend2)
    
    # 打印图信息
    social_graph.print_info()
    
    # 获取邻接矩阵
    adj_matrix, node_mapping = social_graph.get_adjacency_matrix()
    
    print("\n=== 邻接矩阵 ===")
    print("邻接矩阵告诉我们哪些学生是朋友:")
    print("1表示是朋友，0表示不是朋友\n")
    
    # 打印带标签的邻接矩阵
    print("     ", end="")
    for node in social_graph.nodes:
        print(f"{node:>4}", end="")
    print()
    
    for i, node in enumerate(social_graph.nodes):
        print(f"{node:>4} ", end="")
        for j in range(len(social_graph.nodes)):
            print(f"{adj_matrix[i][j]:>4}", end="")
        print()
    
    # 可视化
    social_graph.visualize()
    
    return social_graph, adj_matrix


def understand_adjacency_matrix():
    """
    深入理解邻接矩阵
    """
    print("\n🔍 深入理解邻接矩阵")
    print("=" * 40)
    
    # 创建一个简单的三角形图
    triangle_graph = SimpleGraph()
    
    # 添加三个节点
    for i in range(3):
        triangle_graph.add_node(f"节点{i}")
    
    # 添加三条边，形成三角形
    triangle_graph.add_edge("节点0", "节点1")
    triangle_graph.add_edge("节点1", "节点2")
    triangle_graph.add_edge("节点2", "节点0")
    
    adj_matrix, _ = triangle_graph.get_adjacency_matrix()
    
    print("\n三角形图的邻接矩阵:")
    print(adj_matrix)
    
    print("\n🤔 思考题:")
    print("1. 为什么邻接矩阵是对称的？")
    print("   答：因为这是无向图，如果A和B相连，那么B和A也相连")
    
    print("\n2. 对角线为什么都是0？")
    print("   答：因为节点不与自己相连（没有自环）")
    
    print("\n3. 如何从邻接矩阵找到一个节点的所有邻居？")
    print("   答：看这个节点对应行中值为1的列")
    
    # 演示如何找邻居
    node_idx = 0  # 节点0
    neighbors = np.where(adj_matrix[node_idx] == 1)[0]
    print(f"\n节点{node_idx}的邻居索引: {neighbors}")
    
    triangle_graph.visualize()


if __name__ == "__main__":
    print("🚀 欢迎来到GNN学习第一课！")
    print("我们将从最基础的图概念开始学习\n")
    
    # 第一个例子：社交网络
    social_graph, adj_matrix = create_social_network_example()
    
    # 深入理解邻接矩阵
    understand_adjacency_matrix()
    
    print("\n🎉 恭喜！你已经掌握了图的基本概念！")
    print("下一步我们将学习如何给节点添加特征，为GNN做准备。")
