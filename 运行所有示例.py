"""
GNN学习项目 - 运行所有示例
========================

这个脚本会依次运行所有的学习示例，让你快速体验整个GNN学习过程。
"""

import subprocess
import sys
import time

def run_script(script_path, description):
    """运行单个脚本"""
    print(f"\n{'='*60}")
    print(f"🚀 运行: {description}")
    print(f"📁 文件: {script_path}")
    print(f"{'='*60}")
    
    try:
        # 运行脚本
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ 运行成功！")
            # 只显示最后几行输出，避免过长
            output_lines = result.stdout.split('\n')
            if len(output_lines) > 10:
                print("📄 输出摘要（最后10行）:")
                for line in output_lines[-10:]:
                    if line.strip():
                        print(f"  {line}")
            else:
                print("📄 完整输出:")
                print(result.stdout)
        else:
            print("❌ 运行失败！")
            print("错误信息:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("⏰ 运行超时（超过2分钟）")
    except Exception as e:
        print(f"💥 运行出错: {e}")
    
    print(f"\n⏸️  暂停3秒...")
    time.sleep(3)

def main():
    """主函数"""
    print("🎓 GNN学习项目 - 完整演示")
    print("=" * 60)
    print("这将运行所有的学习示例，展示完整的GNN学习过程")
    print("每个示例运行后会暂停3秒，你可以查看结果")
    print("如果某个示例运行时间过长，会自动超时")
    
    # 定义所有要运行的脚本
    scripts = [
        {
            "path": "01_基础概念/01_图的基本概念.py",
            "description": "第一课：图的基本概念"
        },
        {
            "path": "01_基础概念/02_节点特征和图数据.py", 
            "description": "第二课：节点特征和图数据"
        },
        {
            "path": "02_手工实现/01_手工实现简单GNN.py",
            "description": "第三课：手工实现简单GNN"
        },
        {
            "path": "03_框架学习/03_PyG核心学习.py",
            "description": "第四课：PyTorch Geometric核心学习"
        },
        {
            "path": "04_进阶应用/01_消息传递策略详解.py",
            "description": "第五课：消息传递策略详解"
        }
    ]
    
    print(f"\n📋 将要运行 {len(scripts)} 个示例:")
    for i, script in enumerate(scripts, 1):
        print(f"  {i}. {script['description']}")
    
    # 询问用户是否继续
    response = input(f"\n是否开始运行所有示例？(y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("👋 已取消运行")
        return
    
    # 运行所有脚本
    success_count = 0
    total_count = len(scripts)
    
    for i, script in enumerate(scripts, 1):
        print(f"\n🔢 进度: {i}/{total_count}")
        
        try:
            run_script(script["path"], script["description"])
            success_count += 1
        except KeyboardInterrupt:
            print("\n⛔ 用户中断了运行")
            break
        except Exception as e:
            print(f"💥 运行脚本时出错: {e}")
    
    # 总结
    print(f"\n{'='*60}")
    print(f"🎯 运行总结")
    print(f"{'='*60}")
    print(f"✅ 成功运行: {success_count}/{total_count} 个示例")
    
    if success_count == total_count:
        print("🎉 恭喜！所有示例都运行成功！")
        print("你已经完成了GNN的基础学习之旅！")
    else:
        print("⚠️  部分示例运行失败，请检查环境配置")
    
    print(f"\n📚 下一步建议:")
    print(f"1. 查看 '学习总结和下一步.md' 了解进阶学习路径")
    print(f"2. 尝试在真实数据集上应用GNN")
    print(f"3. 探索更多的GNN变体和应用场景")
    print(f"4. 参与开源项目或技术社区")

if __name__ == "__main__":
    main()
