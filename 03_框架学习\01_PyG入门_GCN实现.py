"""
GNN学习第四课：PyTorch Geometric入门 - GCN实现
============================================

在这个文件中，我们将：
1. 学习PyTorch Geometric的基本概念
2. 实现第一个GCN模型
3. 在Cora数据集上进行节点分类
4. 理解现代GNN框架的使用方法

这是从手工实现到工业级框架的重要一步！
"""

import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv
from torch_geometric.datasets import Planetoid
from torch_geometric.transforms import NormalizeFeatures
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SimpleGCN(torch.nn.Module):
    """
    简单的图卷积网络（GCN）
    """
    
    def __init__(self, input_dim, hidden_dim, output_dim, dropout=0.5):
        super(SimpleGCN, self).__init__()
        
        # 第一层GCN
        self.conv1 = GCNConv(input_dim, hidden_dim)
        
        # 第二层GCN
        self.conv2 = GCNConv(hidden_dim, output_dim)
        
        # Dropout层
        self.dropout = dropout
        
        print(f"🧠 创建GCN模型:")
        print(f"   输入维度: {input_dim}")
        print(f"   隐藏维度: {hidden_dim}")
        print(f"   输出维度: {output_dim}")
        print(f"   Dropout率: {dropout}")
    
    def forward(self, x, edge_index):
        """
        前向传播
        
        参数:
        - x: 节点特征矩阵 [num_nodes, input_dim]
        - edge_index: 边索引 [2, num_edges]
        
        返回:
        - 节点的预测结果 [num_nodes, output_dim]
        """
        # 第一层：特征变换 + 图卷积 + ReLU激活
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        
        # Dropout正则化
        x = F.dropout(x, p=self.dropout, training=self.training)
        
        # 第二层：特征变换 + 图卷积
        x = self.conv2(x, edge_index)
        
        # 输出层使用log_softmax（用于多分类）
        return F.log_softmax(x, dim=1)


def load_cora_dataset():
    """
    加载Cora数据集
    
    Cora是一个经典的论文引用网络数据集：
    - 节点：论文
    - 边：引用关系
    - 特征：论文的词袋表示
    - 标签：论文的研究领域
    """
    print("📚 加载Cora数据集...")
    
    # 加载数据集并进行特征标准化
    dataset = Planetoid(root='数据集/Cora', name='Cora', transform=NormalizeFeatures())
    
    # Cora数据集只有一个图
    data = dataset[0]
    
    print(f"数据集信息:")
    print(f"  图数量: {len(dataset)}")
    print(f"  节点数量: {data.num_nodes}")
    print(f"  边数量: {data.num_edges}")
    print(f"  节点特征维度: {data.num_node_features}")
    print(f"  类别数量: {dataset.num_classes}")
    print(f"  训练节点数量: {data.train_mask.sum()}")
    print(f"  验证节点数量: {data.val_mask.sum()}")
    print(f"  测试节点数量: {data.test_mask.sum()}")
    
    # 打印数据的形状
    print(f"\n数据形状:")
    print(f"  节点特征 x: {data.x.shape}")
    print(f"  边索引 edge_index: {data.edge_index.shape}")
    print(f"  节点标签 y: {data.y.shape}")
    
    return dataset, data


def train_gcn_model(model, data, epochs=200, lr=0.01):
    """
    训练GCN模型
    """
    print(f"\n🚀 开始训练GCN模型 (epochs={epochs}, lr={lr})")
    print("=" * 60)
    
    # 定义优化器和损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=5e-4)
    criterion = torch.nn.NLLLoss()  # 负对数似然损失
    
    # 存储训练历史
    train_losses = []
    train_accuracies = []
    val_accuracies = []
    
    model.train()
    
    for epoch in range(epochs):
        # 前向传播
        optimizer.zero_grad()
        out = model(data.x, data.edge_index)
        
        # 计算训练损失（只在训练节点上）
        loss = criterion(out[data.train_mask], data.y[data.train_mask])
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        # 计算准确率
        train_acc = calculate_accuracy(out[data.train_mask], data.y[data.train_mask])
        val_acc = calculate_accuracy(out[data.val_mask], data.y[data.val_mask])
        
        # 记录历史
        train_losses.append(loss.item())
        train_accuracies.append(train_acc)
        val_accuracies.append(val_acc)
        
        # 每20个epoch打印一次
        if epoch % 20 == 0:
            print(f'Epoch {epoch:03d}: Loss={loss:.4f}, Train Acc={train_acc:.4f}, Val Acc={val_acc:.4f}')
    
    print(f"\n✅ 训练完成！")
    print(f"最终训练准确率: {train_accuracies[-1]:.4f}")
    print(f"最终验证准确率: {val_accuracies[-1]:.4f}")
    
    return train_losses, train_accuracies, val_accuracies


def calculate_accuracy(pred, target):
    """计算准确率"""
    pred_class = pred.argmax(dim=1)
    return (pred_class == target).float().mean().item()


def test_gcn_model(model, data):
    """
    测试GCN模型
    """
    print("\n🧪 测试模型性能...")
    
    model.eval()
    with torch.no_grad():
        out = model(data.x, data.edge_index)
        
        # 在测试集上计算准确率
        test_acc = calculate_accuracy(out[data.test_mask], data.y[data.test_mask])
        
        print(f"测试准确率: {test_acc:.4f}")
        
        # 分析每个类别的性能
        pred_class = out.argmax(dim=1)
        
        print("\n📊 各类别性能分析:")
        for class_id in range(data.y.max().item() + 1):
            class_mask = data.y == class_id
            test_class_mask = class_mask & data.test_mask
            
            if test_class_mask.sum() > 0:
                class_acc = (pred_class[test_class_mask] == data.y[test_class_mask]).float().mean()
                print(f"  类别 {class_id}: 准确率 {class_acc:.4f} ({test_class_mask.sum()}个样本)")
    
    return test_acc


def visualize_training_process(train_losses, train_accuracies, val_accuracies):
    """
    可视化训练过程
    """
    print("\n📈 可视化训练过程...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # 绘制损失曲线
    ax1.plot(train_losses, label='训练损失', color='blue')
    ax1.set_title('训练损失变化', fontsize=14)
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 绘制准确率曲线
    ax2.plot(train_accuracies, label='训练准确率', color='green')
    ax2.plot(val_accuracies, label='验证准确率', color='orange')
    ax2.set_title('准确率变化', fontsize=14)
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()


def analyze_graph_structure(data):
    """
    分析图的结构特性
    """
    print("\n🔍 分析图结构特性...")
    
    # 计算节点度数
    edge_index = data.edge_index
    num_nodes = data.num_nodes
    
    # 计算每个节点的度数
    degrees = torch.zeros(num_nodes, dtype=torch.long)
    for i in range(edge_index.shape[1]):
        degrees[edge_index[0, i]] += 1
    
    print(f"度数统计:")
    print(f"  平均度数: {degrees.float().mean():.2f}")
    print(f"  最大度数: {degrees.max().item()}")
    print(f"  最小度数: {degrees.min().item()}")
    
    # 可视化度数分布
    plt.figure(figsize=(10, 6))
    plt.hist(degrees.numpy(), bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title('节点度数分布', fontsize=16)
    plt.xlabel('度数')
    plt.ylabel('节点数量')
    plt.grid(True, alpha=0.3)
    plt.show()


def compare_with_baseline(data):
    """
    与基线方法比较
    """
    print("\n📊 与基线方法比较...")
    
    # 基线1：随机猜测
    num_classes = data.y.max().item() + 1
    random_acc = 1.0 / num_classes
    
    # 基线2：多数类预测
    train_labels = data.y[data.train_mask]
    majority_class = train_labels.mode()[0]
    majority_acc = (data.y[data.test_mask] == majority_class).float().mean().item()
    
    print(f"基线方法性能:")
    print(f"  随机猜测: {random_acc:.4f}")
    print(f"  多数类预测: {majority_acc:.4f}")
    
    return random_acc, majority_acc


if __name__ == "__main__":
    print("🚀 欢迎来到GNN学习第四课！")
    print("今天我们使用PyTorch Geometric实现第一个GCN模型\n")
    
    # 设置随机种子
    torch.manual_seed(42)
    
    # 加载数据集
    dataset, data = load_cora_dataset()
    
    # 分析图结构
    analyze_graph_structure(data)
    
    # 创建GCN模型
    model = SimpleGCN(
        input_dim=dataset.num_node_features,
        hidden_dim=16,
        output_dim=dataset.num_classes,
        dropout=0.5
    )
    
    print(f"\n模型参数数量: {sum(p.numel() for p in model.parameters())}")
    
    # 训练模型
    train_losses, train_accs, val_accs = train_gcn_model(model, data, epochs=200)
    
    # 测试模型
    test_acc = test_gcn_model(model, data)
    
    # 与基线比较
    random_acc, majority_acc = compare_with_baseline(data)
    
    # 可视化训练过程
    visualize_training_process(train_losses, train_accs, val_accs)
    
    print(f"\n🎯 最终结果总结:")
    print(f"  随机猜测准确率: {random_acc:.4f}")
    print(f"  多数类预测准确率: {majority_acc:.4f}")
    print(f"  GCN模型准确率: {test_acc:.4f}")
    print(f"  相对于随机的提升: {(test_acc - random_acc) / random_acc * 100:.1f}%")
    
    print("\n🎉 恭喜！你已经成功实现了第一个GCN模型！")
    print("下一步我们将学习更多的GNN变体和消息传递策略！")
