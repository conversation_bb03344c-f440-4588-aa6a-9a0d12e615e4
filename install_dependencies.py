"""
安装GNN学习所需的依赖包
"""

import subprocess
import sys

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装: {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ 安装失败: {package}")
        return False

def main():
    print("🚀 开始安装GNN学习依赖包...")
    print("=" * 50)
    
    # 基础包
    basic_packages = [
        "numpy",
        "matplotlib", 
        "pandas",
        "networkx",
        "scikit-learn",
        "scipy",
        "tqdm",
        "seaborn"
    ]
    
    print("📦 安装基础科学计算包...")
    for package in basic_packages:
        install_package(package)
    
    print("\n🔥 安装PyTorch...")
    # 安装PyTorch (CPU版本)
    torch_packages = [
        "torch",
        "torchvision", 
        "torchaudio"
    ]
    
    for package in torch_packages:
        install_package(package)
    
    print("\n🧠 安装PyTorch Geometric...")
    # 安装PyTorch Geometric
    pyg_packages = [
        "torch-geometric"
    ]
    
    for package in pyg_packages:
        install_package(package)
    
    print("\n🎉 依赖安装完成！")
    print("现在你可以运行GNN学习代码了！")

if __name__ == "__main__":
    main()
