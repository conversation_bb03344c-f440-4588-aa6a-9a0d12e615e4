"""
GNN学习第五课：消息传递策略详解
==============================

深入理解不同的消息传递策略：
1. GCN的谱域方法
2. GAT的注意力机制
3. GraphSAGE的采样聚合
4. 自定义消息传递层
5. 策略对比和选择指南
"""

import torch
import torch.nn.functional as F
from torch_geometric.nn import MessagePassing, GCNConv, GATConv, SAGEConv
from torch_geometric.data import Data
from torch_geometric.utils import add_self_loops, degree
import numpy as np

class CustomGCNConv(MessagePassing):
    """
    自定义实现GCN层，帮助理解消息传递机制
    """
    
    def __init__(self, in_channels, out_channels):
        super(CustomGCNConv, self).__init__(aggr='add')  # 使用求和聚合
        self.lin = torch.nn.Linear(in_channels, out_channels)
        
    def forward(self, x, edge_index):
        # 步骤1: 添加自环
        edge_index, _ = add_self_loops(edge_index, num_nodes=x.size(0))
        
        # 步骤2: 线性变换
        x = self.lin(x)
        
        # 步骤3: 计算归一化系数
        row, col = edge_index
        deg = degree(col, x.size(0), dtype=x.dtype)
        deg_inv_sqrt = deg.pow(-0.5)
        deg_inv_sqrt[deg_inv_sqrt == float('inf')] = 0
        norm = deg_inv_sqrt[row] * deg_inv_sqrt[col]
        
        # 步骤4: 开始消息传递
        return self.propagate(edge_index, x=x, norm=norm)
    
    def message(self, x_j, norm):
        # 消息函数：对邻居特征进行归一化
        return norm.view(-1, 1) * x_j
    
    def update(self, aggr_out):
        # 更新函数：直接返回聚合结果
        return aggr_out


class CustomGATConv(MessagePassing):
    """
    自定义实现GAT层，展示注意力机制
    """
    
    def __init__(self, in_channels, out_channels, heads=1, dropout=0.0):
        super(CustomGATConv, self).__init__(aggr='add')
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.heads = heads
        self.dropout = dropout
        
        # 线性变换层
        self.lin = torch.nn.Linear(in_channels, heads * out_channels)
        
        # 注意力参数
        self.att = torch.nn.Parameter(torch.Tensor(1, heads, 2 * out_channels))
        
        self.reset_parameters()
    
    def reset_parameters(self):
        torch.nn.init.xavier_uniform_(self.lin.weight)
        torch.nn.init.xavier_uniform_(self.att)
    
    def forward(self, x, edge_index):
        # 线性变换
        x = self.lin(x).view(-1, self.heads, self.out_channels)
        
        # 开始消息传递
        return self.propagate(edge_index, x=x)
    
    def message(self, x_i, x_j, edge_index_i):
        # 计算注意力系数
        alpha = (torch.cat([x_i, x_j], dim=-1) * self.att).sum(dim=-1)
        alpha = F.leaky_relu(alpha, 0.2)
        alpha = F.softmax(alpha, dim=1)
        alpha = F.dropout(alpha, p=self.dropout, training=self.training)
        
        # 应用注意力权重
        return alpha.unsqueeze(-1) * x_j
    
    def update(self, aggr_out):
        # 如果有多个头，可以选择平均或拼接
        if self.heads > 1:
            aggr_out = aggr_out.mean(dim=1)
        else:
            aggr_out = aggr_out.squeeze(1)
        return aggr_out


class MessagePassingAnalyzer:
    """
    消息传递分析器，用于可视化和理解不同策略
    """
    
    def __init__(self, data):
        self.data = data
        self.num_nodes = data.num_nodes
        self.edge_index = data.edge_index
    
    def analyze_gcn_propagation(self, x):
        """分析GCN的消息传递过程"""
        print("🔍 GCN消息传递分析")
        print("=" * 40)
        
        # 计算度数
        row, col = self.edge_index
        deg = degree(col, self.num_nodes, dtype=x.dtype)
        
        print("节点度数:")
        for i in range(self.num_nodes):
            print(f"  节点{i}: 度数={deg[i].item():.0f}")
        
        # 计算归一化系数
        deg_inv_sqrt = deg.pow(-0.5)
        deg_inv_sqrt[deg_inv_sqrt == float('inf')] = 0
        
        print("\n归一化系数 (1/√度数):")
        for i in range(self.num_nodes):
            print(f"  节点{i}: {deg_inv_sqrt[i].item():.4f}")
        
        # 模拟一轮消息传递
        print("\n模拟消息传递:")
        aggregated = torch.zeros_like(x)
        
        for node in range(self.num_nodes):
            neighbors = []
            for i in range(self.edge_index.shape[1]):
                if self.edge_index[1, i].item() == node:
                    neighbors.append(self.edge_index[0, i].item())
            
            if neighbors:
                # GCN的聚合方式：加权求和
                node_sum = torch.zeros_like(x[0])
                for neighbor in neighbors:
                    weight = deg_inv_sqrt[neighbor] * deg_inv_sqrt[node]
                    node_sum += weight * x[neighbor]
                
                aggregated[node] = node_sum
                print(f"  节点{node}: 邻居{neighbors} -> 聚合特征均值: {node_sum.mean().item():.4f}")
        
        return aggregated
    
    def analyze_attention_weights(self, model, x):
        """分析GAT的注意力权重"""
        print("\n🎯 GAT注意力权重分析")
        print("=" * 40)
        
        if not isinstance(model, CustomGATConv):
            print("需要CustomGATConv模型来分析注意力权重")
            return
        
        # 这里可以添加注意力权重的提取和分析
        print("注意力权重分析需要在模型中添加钩子函数")
        print("这是一个高级话题，可以在后续学习中深入")
    
    def compare_aggregation_strategies(self, x):
        """比较不同的聚合策略"""
        print("\n📊 比较聚合策略")
        print("=" * 40)
        
        strategies = {
            'mean': lambda features: torch.mean(features, dim=0),
            'sum': lambda features: torch.sum(features, dim=0),
            'max': lambda features: torch.max(features, dim=0)[0],
            'min': lambda features: torch.min(features, dim=0)[0]
        }
        
        for node in range(min(3, self.num_nodes)):  # 只分析前3个节点
            neighbors = []
            for i in range(self.edge_index.shape[1]):
                if self.edge_index[1, i].item() == node:
                    neighbors.append(self.edge_index[0, i].item())
            
            if neighbors:
                neighbor_features = x[neighbors]
                print(f"\n节点{node}的邻居{neighbors}:")
                
                for strategy_name, strategy_func in strategies.items():
                    result = strategy_func(neighbor_features)
                    print(f"  {strategy_name:>4}: {result.mean().item():.4f}")


def create_test_graph():
    """创建测试图"""
    print("🔧 创建测试图...")
    
    # 创建一个小的测试图
    x = torch.FloatTensor([
        [1.0, 0.0],  # 节点0
        [0.0, 1.0],  # 节点1
        [1.0, 1.0],  # 节点2
        [0.5, 0.5],  # 节点3
        [0.0, 0.0],  # 节点4
    ])
    
    # 创建星形图：节点2在中心
    edge_index = torch.LongTensor([
        [0, 1, 2, 2, 2, 3, 4],  # 源节点
        [2, 2, 0, 1, 3, 2, 2],  # 目标节点
    ])
    
    y = torch.LongTensor([0, 0, 1, 1, 0])
    
    data = Data(x=x, edge_index=edge_index, y=y)
    
    print(f"✅ 测试图创建完成:")
    print(f"  节点数: {data.num_nodes}")
    print(f"  边数: {data.num_edges}")
    print(f"  特征维度: {data.num_node_features}")
    
    return data


def compare_message_passing_layers(data):
    """比较不同的消息传递层"""
    print("\n🏆 比较消息传递层")
    print("=" * 50)
    
    input_dim = data.num_node_features
    output_dim = 2
    
    # 创建不同的层
    layers = {
        'Custom GCN': CustomGCNConv(input_dim, output_dim),
        'PyG GCN': GCNConv(input_dim, output_dim),
        'Custom GAT': CustomGATConv(input_dim, output_dim, heads=2),
        'PyG GAT': GATConv(input_dim, output_dim, heads=2),
        'PyG SAGE': SAGEConv(input_dim, output_dim)
    }
    
    print("各层的输出对比:")
    
    for name, layer in layers.items():
        with torch.no_grad():
            try:
                output = layer(data.x, data.edge_index)
                print(f"\n{name}:")
                print(f"  输出形状: {output.shape}")
                print(f"  输出均值: {output.mean().item():.4f}")
                print(f"  输出标准差: {output.std().item():.4f}")
                
                # 显示每个节点的输出
                for i in range(data.num_nodes):
                    print(f"  节点{i}: {output[i].numpy()}")
                    
            except Exception as e:
                print(f"{name}: 错误 - {e}")


def demonstrate_message_passing_steps():
    """演示消息传递的详细步骤"""
    print("\n🔄 详细演示消息传递步骤")
    print("=" * 50)
    
    # 创建简单的3节点图
    x = torch.FloatTensor([[1.0], [2.0], [3.0]])  # 节点特征
    edge_index = torch.LongTensor([[0, 1, 2], [1, 2, 0]])  # 环形图
    
    print("图结构: 0 -> 1 -> 2 -> 0 (环形)")
    print(f"节点特征: {x.flatten().tolist()}")
    
    print("\n步骤1: 消息生成")
    print("每条边生成消息:")
    for i in range(edge_index.shape[1]):
        src, dst = edge_index[0, i].item(), edge_index[1, i].item()
        message = x[src]
        print(f"  边 {src}->{dst}: 消息 = {message.item()}")
    
    print("\n步骤2: 消息聚合")
    print("每个节点聚合收到的消息:")
    
    aggregated = torch.zeros_like(x)
    for node in range(3):
        messages = []
        for i in range(edge_index.shape[1]):
            if edge_index[1, i].item() == node:
                src = edge_index[0, i].item()
                messages.append(x[src].item())
        
        if messages:
            agg_value = sum(messages) / len(messages)  # 平均聚合
            aggregated[node] = agg_value
            print(f"  节点{node}: 收到消息{messages} -> 聚合结果 = {agg_value}")
    
    print("\n步骤3: 状态更新")
    print("结合原始特征和聚合消息:")
    
    updated = x + aggregated  # 简单相加
    for node in range(3):
        print(f"  节点{node}: {x[node].item()} + {aggregated[node].item()} = {updated[node].item()}")
    
    return updated


def message_passing_best_practices():
    """消息传递最佳实践指南"""
    print("\n📚 消息传递策略选择指南")
    print("=" * 50)
    
    practices = {
        "GCN (图卷积网络)": {
            "适用场景": "节点特征相对均匀，图结构规整",
            "优点": "计算效率高，理论基础扎实",
            "缺点": "无法区分邻居重要性，容易过平滑",
            "推荐用途": "社交网络分析，引用网络"
        },
        
        "GAT (图注意力网络)": {
            "适用场景": "邻居重要性差异很大，需要动态权重",
            "优点": "能学习邻居重要性，表达能力强",
            "缺点": "计算复杂度高，容易过拟合",
            "推荐用途": "知识图谱，异构图分析"
        },
        
        "GraphSAGE": {
            "适用场景": "大规模图，需要归纳学习",
            "优点": "可扩展性好，支持未见过的节点",
            "缺点": "采样可能丢失信息",
            "推荐用途": "推荐系统，大规模网络分析"
        }
    }
    
    for method, info in practices.items():
        print(f"\n{method}:")
        for key, value in info.items():
            print(f"  {key}: {value}")
    
    print("\n💡 选择建议:")
    print("1. 小图 + 均匀特征 -> GCN")
    print("2. 异构图 + 重要性差异 -> GAT") 
    print("3. 大图 + 归纳学习 -> GraphSAGE")
    print("4. 实验对比多种方法，选择最佳")


if __name__ == "__main__":
    print("🚀 欢迎来到GNN消息传递策略详解！")
    print("深入理解不同策略的工作原理和适用场景\n")
    
    # 设置随机种子
    torch.manual_seed(42)
    
    # 创建测试图
    data = create_test_graph()
    
    # 创建分析器
    analyzer = MessagePassingAnalyzer(data)
    
    # 分析GCN消息传递
    gcn_result = analyzer.analyze_gcn_propagation(data.x)
    
    # 比较聚合策略
    analyzer.compare_aggregation_strategies(data.x)
    
    # 比较不同的消息传递层
    compare_message_passing_layers(data)
    
    # 演示详细的消息传递步骤
    demonstrate_message_passing_steps()
    
    # 最佳实践指南
    message_passing_best_practices()
    
    print("\n🎉 恭喜！你已经深入理解了GNN的消息传递机制！")
    print("现在你可以根据具体问题选择合适的GNN策略了！")
