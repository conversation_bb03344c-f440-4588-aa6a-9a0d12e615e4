"""
GNN学习成果展示：社交网络影响力分析（简化版）
==========================================

这个案例展示如何使用图神经网络分析社交网络中的用户影响力。
专注于核心功能，避免复杂的可视化问题。
"""

import torch
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, SAGEConv
from torch_geometric.data import Data
import numpy as np
import networkx as nx
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SocialNetworkGenerator:
    """社交网络数据生成器"""
    
    def __init__(self, num_users=100, seed=42):
        self.num_users = num_users
        self.seed = seed
        np.random.seed(seed)
        torch.manual_seed(seed)
    
    def generate_network(self):
        """生成社交网络数据"""
        print("🌐 生成社交网络数据...")
        
        # 生成用户特征：[粉丝数, 发帖数, 活跃度, 账户年龄, 认证状态]
        features = []
        for i in range(self.num_users):
            followers = np.random.lognormal(mean=3, sigma=1)  # 粉丝数
            posts = np.random.poisson(lam=30)  # 发帖数
            activity = np.random.beta(a=2, b=2)  # 活跃度
            account_age = np.random.uniform(1, 8)  # 账户年龄
            verified = np.random.choice([0, 1], p=[0.9, 0.1])  # 认证状态
            
            features.append([followers, posts, activity, account_age, verified])
        
        # 标准化特征
        features = np.array(features)
        features = (features - features.mean(axis=0)) / (features.std(axis=0) + 1e-8)
        x = torch.FloatTensor(features)
        
        # 生成网络结构（小世界网络）
        G = nx.watts_strogatz_graph(self.num_users, k=6, p=0.3, seed=self.seed)
        edge_list = list(G.edges())
        edge_index = torch.LongTensor(edge_list).t().contiguous()
        
        # 计算影响力标签
        degrees = torch.zeros(self.num_users)
        for i in range(edge_index.shape[1]):
            degrees[edge_index[0, i]] += 1
        
        # 综合计算影响力分数
        influence_score = (
            x[:, 0] * 0.3 +  # 粉丝数
            x[:, 1] * 0.2 +  # 发帖数
            x[:, 2] * 0.2 +  # 活跃度
            x[:, 4] * 0.1 +  # 认证状态
            (degrees / degrees.max()) * 0.2  # 网络中心性
        )
        
        # 转换为分类标签
        y = torch.zeros(self.num_users, dtype=torch.long)
        thresholds = torch.quantile(influence_score, torch.tensor([0.33, 0.67]))
        y[influence_score >= thresholds[1]] = 2  # 高影响力
        y[(influence_score >= thresholds[0]) & (influence_score < thresholds[1])] = 1  # 中影响力
        y[influence_score < thresholds[0]] = 0  # 低影响力
        
        # 创建训练/测试掩码
        indices = torch.randperm(self.num_users)
        train_size = int(0.7 * self.num_users)
        
        train_mask = torch.zeros(self.num_users, dtype=torch.bool)
        test_mask = torch.zeros(self.num_users, dtype=torch.bool)
        train_mask[indices[:train_size]] = True
        test_mask[indices[train_size:]] = True
        
        data = Data(x=x, edge_index=edge_index, y=y, train_mask=train_mask, test_mask=test_mask)
        
        print(f"✅ 网络生成完成:")
        print(f"  用户数量: {data.num_nodes}")
        print(f"  连接数量: {data.num_edges}")
        print(f"  特征维度: {data.num_node_features}")
        print(f"  训练用户: {train_mask.sum()}")
        print(f"  测试用户: {test_mask.sum()}")
        
        return data


class InfluenceGNN(torch.nn.Module):
    """影响力预测GNN模型"""
    
    def __init__(self, input_dim, hidden_dim, output_dim, model_type='GCN'):
        super(InfluenceGNN, self).__init__()
        
        self.model_type = model_type
        
        if model_type == 'GCN':
            self.conv1 = GCNConv(input_dim, hidden_dim)
            self.conv2 = GCNConv(hidden_dim, output_dim)
        elif model_type == 'GAT':
            self.conv1 = GATConv(input_dim, hidden_dim, heads=4)
            self.conv2 = GATConv(hidden_dim * 4, output_dim, heads=1)
        elif model_type == 'SAGE':
            self.conv1 = SAGEConv(input_dim, hidden_dim)
            self.conv2 = SAGEConv(hidden_dim, output_dim)
        
        print(f"🧠 创建 {model_type} 模型: {input_dim} -> {hidden_dim} -> {output_dim}")
    
    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = F.dropout(x, p=0.5, training=self.training)
        x = self.conv2(x, edge_index)
        return F.log_softmax(x, dim=1)


def train_and_evaluate_model(model, data, epochs=150):
    """训练和评估模型"""
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01, weight_decay=5e-4)
    criterion = torch.nn.NLLLoss()
    
    train_losses = []
    train_accs = []
    
    # 训练
    model.train()
    for epoch in range(epochs):
        optimizer.zero_grad()
        out = model(data.x, data.edge_index)
        loss = criterion(out[data.train_mask], data.y[data.train_mask])
        loss.backward()
        optimizer.step()
        
        # 记录指标
        train_acc = accuracy_score(
            data.y[data.train_mask].cpu(),
            out[data.train_mask].argmax(dim=1).cpu()
        )
        train_losses.append(loss.item())
        train_accs.append(train_acc)
        
        if epoch % 30 == 0:
            print(f'  Epoch {epoch:03d}: Loss={loss:.4f}, Train Acc={train_acc:.4f}')
    
    # 评估
    model.eval()
    with torch.no_grad():
        out = model(data.x, data.edge_index)
        test_pred = out[data.test_mask].argmax(dim=1)
        test_true = data.y[data.test_mask]
        test_acc = accuracy_score(test_true.cpu(), test_pred.cpu())
    
    return {
        'train_losses': train_losses,
        'train_accs': train_accs,
        'test_accuracy': test_acc,
        'test_pred': test_pred,
        'test_true': test_true,
        'predictions': out
    }


def create_simple_visualization(data, results):
    """创建简单的可视化"""
    print("\n📊 创建可视化展示...")
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 模型性能对比
    ax1 = axes[0, 0]
    models = list(results.keys())
    accuracies = [results[model]['test_accuracy'] for model in models]
    
    bars = ax1.bar(models, accuracies, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
    for bar, acc in zip(bars, accuracies):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')
    
    ax1.set_title('模型性能对比', fontsize=14)
    ax1.set_ylabel('测试准确率')
    ax1.set_ylim(0, 1)
    ax1.grid(True, alpha=0.3)
    
    # 2. 训练损失
    ax2 = axes[0, 1]
    for model_name, result in results.items():
        ax2.plot(result['train_losses'], label=f'{model_name}', alpha=0.8)
    ax2.set_title('训练损失变化', fontsize=14)
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 训练准确率
    ax3 = axes[0, 2]
    for model_name, result in results.items():
        ax3.plot(result['train_accs'], label=f'{model_name}', alpha=0.8)
    ax3.set_title('训练准确率变化', fontsize=14)
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Accuracy')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4-6. 混淆矩阵
    for i, (model_name, result) in enumerate(results.items()):
        ax = axes[1, i]
        cm = confusion_matrix(result['test_true'].cpu(), result['test_pred'].cpu())
        
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax,
                   xticklabels=['低', '中', '高'],
                   yticklabels=['低', '中', '高'])
        
        ax.set_title(f'{model_name} 混淆矩阵', fontsize=12)
        ax.set_xlabel('预测标签')
        ax.set_ylabel('真实标签')
    
    plt.tight_layout()
    plt.savefig('GNN学习成果展示_简化版.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 可视化图表已保存为 'GNN学习成果展示_简化版.png'")


def main():
    """主函数"""
    print("🎓 GNN学习成果展示：社交网络影响力分析")
    print("=" * 60)
    
    # 1. 生成数据
    generator = SocialNetworkGenerator(num_users=100, seed=42)
    data = generator.generate_network()
    
    # 2. 训练模型
    print(f"\n🏆 训练和比较不同GNN模型")
    print("=" * 50)
    
    models = {
        'GCN': InfluenceGNN(data.num_node_features, 16, 3, 'GCN'),
        'GAT': InfluenceGNN(data.num_node_features, 8, 3, 'GAT'),
        'GraphSAGE': InfluenceGNN(data.num_node_features, 16, 3, 'SAGE')
    }
    
    results = {}
    for name, model in models.items():
        print(f"\n--- 训练 {name} ---")
        result = train_and_evaluate_model(model, data, epochs=120)
        results[name] = result
        print(f"✅ {name} 测试准确率: {result['test_accuracy']:.4f}")
    
    # 3. 可视化结果
    create_simple_visualization(data, results)
    
    # 4. 详细分析
    print(f"\n📈 详细结果分析:")
    print("=" * 40)
    
    for model_name, result in results.items():
        print(f"\n{model_name} 模型:")
        print(f"  测试准确率: {result['test_accuracy']:.4f}")
        
        report = classification_report(
            result['test_true'].cpu(), 
            result['test_pred'].cpu(),
            target_names=['低影响力', '中影响力', '高影响力'],
            output_dict=True
        )
        
        print(f"  精确率: {report['macro avg']['precision']:.4f}")
        print(f"  召回率: {report['macro avg']['recall']:.4f}")
        print(f"  F1分数: {report['macro avg']['f1-score']:.4f}")
    
    best_model = max(results.keys(), key=lambda x: results[x]['test_accuracy'])
    print(f"\n🏆 最佳模型: {best_model}")
    print(f"🎯 最高准确率: {results[best_model]['test_accuracy']:.4f}")
    
    print(f"\n🎉 学习成果展示完成！")
    print(f"这个案例展示了完整的GNN应用流程：")
    print(f"• 数据生成和预处理")
    print(f"• 多种GNN模型的实现和训练")
    print(f"• 模型性能评估和对比")
    print(f"• 结果可视化和分析")


if __name__ == "__main__":
    main()
