"""
GNN学习第二课：节点特征和图数据
=============================

在这个文件中，我们将学习：
1. 什么是节点特征
2. 如何表示图数据
3. 特征矩阵的概念
4. 为GNN准备数据

这是理解GNN的关键一步！
"""

import numpy as np
import matplotlib.pyplot as plt
import networkx as nx
from sklearn.preprocessing import StandardScaler

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class GraphWithFeatures:
    """
    带有节点特征的图类
    """
    
    def __init__(self):
        self.nodes = []
        self.edges = []
        self.node_features = {}  # 存储每个节点的特征向量
        self.feature_names = []  # 特征名称
    
    def add_node(self, node_id, features):
        """添加带特征的节点"""
        if node_id not in self.nodes:
            self.nodes.append(node_id)
            self.node_features[node_id] = np.array(features)
            print(f"添加节点 {node_id}，特征: {features}")
    
    def add_edge(self, node1, node2):
        """添加边"""
        if node1 in self.nodes and node2 in self.nodes:
            edge = (node1, node2)
            if edge not in self.edges and (node2, node1) not in self.edges:
                self.edges.append(edge)
    
    def get_feature_matrix(self):
        """获取特征矩阵"""
        if not self.nodes:
            return np.array([])
        
        # 按节点顺序排列特征
        features = []
        for node in self.nodes:
            features.append(self.node_features[node])
        
        return np.array(features)
    
    def get_adjacency_matrix(self):
        """获取邻接矩阵"""
        n = len(self.nodes)
        node_to_idx = {node: i for i, node in enumerate(self.nodes)}
        
        adj_matrix = np.zeros((n, n), dtype=int)
        
        for node1, node2 in self.edges:
            i, j = node_to_idx[node1], node_to_idx[node2]
            adj_matrix[i][j] = 1
            adj_matrix[j][i] = 1
        
        return adj_matrix, node_to_idx
    
    def visualize_with_features(self, feature_idx=0):
        """根据某个特征可视化图"""
        G = nx.Graph()
        G.add_nodes_from(self.nodes)
        G.add_edges_from(self.edges)
        
        # 获取指定特征作为节点颜色
        feature_values = [self.node_features[node][feature_idx] for node in self.nodes]
        
        plt.figure(figsize=(12, 8))
        
        pos = nx.spring_layout(G)
        
        # 绘制节点，颜色表示特征值
        nodes = nx.draw_networkx_nodes(G, pos, node_color=feature_values, 
                                     node_size=1000, cmap='viridis')
        
        # 绘制边和标签
        nx.draw_networkx_edges(G, pos)
        nx.draw_networkx_labels(G, pos, font_size=12, font_weight='bold')
        
        # 添加颜色条
        plt.colorbar(nodes, label=f'特征 {feature_idx}')
        plt.title(f"图可视化 - 按特征{feature_idx}着色", fontsize=16)
        plt.axis('off')
        plt.show()
    
    def print_graph_data(self):
        """打印图数据的完整信息"""
        print("\n=== 图数据信息 ===")
        print(f"节点数量: {len(self.nodes)}")
        print(f"边数量: {len(self.edges)}")
        
        # 打印特征矩阵
        feature_matrix = self.get_feature_matrix()
        print(f"\n特征矩阵形状: {feature_matrix.shape}")
        print("特征矩阵:")
        print(feature_matrix)
        
        # 打印邻接矩阵
        adj_matrix, _ = self.get_adjacency_matrix()
        print(f"\n邻接矩阵形状: {adj_matrix.shape}")
        print("邻接矩阵:")
        print(adj_matrix)


def create_student_network_with_features():
    """
    创建带有学生特征的社交网络
    特征包括：年龄、成绩、兴趣爱好数量
    """
    print("🎓 创建学生特征网络")
    print("=" * 40)
    
    graph = GraphWithFeatures()
    graph.feature_names = ["年龄", "成绩", "兴趣爱好数量"]
    
    # 学生数据：[年龄, 成绩(0-100), 兴趣爱好数量]
    students_data = {
        "小明": [16, 85, 3],  # 16岁，85分，3个兴趣爱好
        "小红": [15, 92, 5],  # 15岁，92分，5个兴趣爱好
        "小刚": [17, 78, 2],  # 17岁，78分，2个兴趣爱好
        "小丽": [16, 88, 4],  # 16岁，88分，4个兴趣爱好
        "小华": [15, 95, 6],  # 15岁，95分，6个兴趣爱好
    }
    
    # 添加学生节点
    for name, features in students_data.items():
        graph.add_node(name, features)
    
    # 添加朋友关系
    friendships = [
        ("小明", "小红"),
        ("小明", "小刚"),
        ("小红", "小丽"),
        ("小刚", "小华"),
        ("小丽", "小华"),
    ]
    
    for friend1, friend2 in friendships:
        graph.add_edge(friend1, friend2)
    
    # 打印图数据
    graph.print_graph_data()
    
    # 可视化不同特征
    print("\n📊 可视化不同特征:")
    for i, feature_name in enumerate(graph.feature_names):
        print(f"按{feature_name}着色...")
        graph.visualize_with_features(feature_idx=i)
    
    return graph


def understand_feature_aggregation():
    """
    理解特征聚合 - GNN的核心概念
    """
    print("\n🧠 理解特征聚合")
    print("=" * 40)
    
    # 创建简单的三节点图
    graph = GraphWithFeatures()
    
    # 添加节点和特征
    graph.add_node("A", [1.0, 2.0])  # 节点A的特征
    graph.add_node("B", [3.0, 1.0])  # 节点B的特征
    graph.add_node("C", [2.0, 3.0])  # 节点C的特征
    
    # 添加边：A连接B和C
    graph.add_edge("A", "B")
    graph.add_edge("A", "C")
    
    # 获取数据
    feature_matrix = graph.get_feature_matrix()
    adj_matrix, node_mapping = graph.get_adjacency_matrix()
    
    print("特征矩阵:")
    print(feature_matrix)
    print("\n邻接矩阵:")
    print(adj_matrix)
    
    # 手动演示消息传递
    print("\n🔄 手动演示消息传递过程:")
    print("假设我们要更新节点A的特征...")
    
    # 找到节点A的邻居
    node_A_idx = node_mapping["A"]
    neighbors_mask = adj_matrix[node_A_idx] == 1
    neighbor_indices = np.where(neighbors_mask)[0]
    
    print(f"节点A的索引: {node_A_idx}")
    print(f"节点A的邻居索引: {neighbor_indices}")
    
    # 聚合邻居特征（简单平均）
    neighbor_features = feature_matrix[neighbor_indices]
    print(f"邻居特征:\n{neighbor_features}")
    
    aggregated_features = np.mean(neighbor_features, axis=0)
    print(f"聚合后的特征（平均）: {aggregated_features}")
    
    # 更新节点A的特征（简单相加）
    old_features = feature_matrix[node_A_idx]
    new_features = old_features + aggregated_features
    print(f"节点A原始特征: {old_features}")
    print(f"节点A新特征: {new_features}")
    
    print("\n💡 这就是GNN的基本思想：")
    print("1. 收集邻居的特征信息")
    print("2. 聚合这些信息（平均、求和、最大值等）")
    print("3. 结合自己的特征，更新节点状态")


def prepare_data_for_gnn():
    """
    为GNN准备标准格式的数据
    """
    print("\n📦 为GNN准备数据")
    print("=" * 40)
    
    # 创建一个更大的图
    graph = GraphWithFeatures()
    
    # 模拟论文引用网络
    papers = {
        "论文A": [2020, 15, 3],  # [发表年份, 引用数, 作者数]
        "论文B": [2019, 25, 2],
        "论文C": [2021, 8, 4],
        "论文D": [2020, 30, 1],
        "论文E": [2018, 45, 3],
    }
    
    for paper, features in papers.items():
        graph.add_node(paper, features)
    
    # 引用关系
    citations = [
        ("论文A", "论文B"),  # A引用B
        ("论文A", "论文E"),  # A引用E
        ("论文B", "论文E"),  # B引用E
        ("论文C", "论文A"),  # C引用A
        ("论文D", "论文B"),  # D引用B
    ]
    
    for paper1, paper2 in citations:
        graph.add_edge(paper1, paper2)
    
    # 获取标准格式数据
    X = graph.get_feature_matrix()  # 特征矩阵
    A = graph.get_adjacency_matrix()[0]  # 邻接矩阵
    
    print("🎯 GNN标准输入格式:")
    print(f"特征矩阵 X 形状: {X.shape}")
    print(f"邻接矩阵 A 形状: {A.shape}")
    
    print("\n特征矩阵 X (每行是一个节点的特征):")
    print(X)
    
    print("\n邻接矩阵 A (表示节点间的连接关系):")
    print(A)
    
    # 标准化特征
    scaler = StandardScaler()
    X_normalized = scaler.fit_transform(X)
    
    print("\n标准化后的特征矩阵:")
    print(X_normalized)
    
    print("\n✅ 数据准备完成！这就是GNN需要的输入格式。")
    
    return X, A, X_normalized


if __name__ == "__main__":
    print("🚀 欢迎来到GNN学习第二课！")
    print("今天我们学习节点特征和图数据表示\n")
    
    # 创建带特征的学生网络
    student_graph = create_student_network_with_features()
    
    # 理解特征聚合
    understand_feature_aggregation()
    
    # 准备GNN数据
    X, A, X_norm = prepare_data_for_gnn()
    
    print("\n🎉 恭喜！你已经理解了图数据的表示方法！")
    print("下一步我们将手工实现第一个简单的GNN！")
